  import React, { useState, useRef, useEffect, useCallback } from 'react';
  import { Icon } from '@iconify/react';
  import { useNavigate } from 'react-router-dom';
  import { allCourses } from '../../../services/userService';
  import NoData from '../../../components/common/NoData';
  import './Course.css';
  import { encodeData } from '../../../utils/encodeAndEncode'; // adjust path if needed

  // Helper function to get currency symbol
  const getCurrencySymbol = (currency) => {
    switch (currency?.toUpperCase()) {
      case 'INR':
        return '₹';
      case 'USD':
        return '$';
      case 'SGD':
        return 'S$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      default:
        return '$'; // Default to USD symbol
    }
  };

  function CourseTab() {
    const navigate = useNavigate();
    const observer = useRef();
    const [courses, setCourses] = useState([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [clickedCourseId, setClickedCourseId] = useState(null);
    const searchTimeout = useRef(null);

    const fetchCourses = async (pageNum = 1, reset = false, search = '') => {
      try {
        setIsLoading(true);
        const start = Date.now();

        const response = await allCourses({ page: pageNum, limit: 10, search });
        console.log("Response ------------", response);
        const elapsed = Date.now() - start;
        const delay = 500 - elapsed;

        const process = () => {
          if (response.success && response.data?.courses) {
            const newCourses = response.data.courses.map((course, index) => ({
              id: course.id,
              image: course.banner_image,
              title: course.course_name || 'Untitled Course',
              description: course.course_desc || 'No description provided.',
              modules: response.data.metadata[index]?.totalModules || 0,
              enrolled: response.data.metadata[index]?.totalUsers || 0,
              duration: response.data.metadata[index]?.duration || '—',
              rating: course.total_rating || 0,
              level: course.levels || 'N/A',
              price: course.course_type?.toLowerCase() === 'free' ? 'Free' : course.course_price || '0.00',
              course_type: course.course_type,
              currency: course.currency || 'USD'
            }));
            setCourses(prev => reset ? newCourses : [...prev, ...newCourses]);
            setHasMore(pageNum < response.data.totalPages);
          } else {
            setHasMore(false);
          }
          setIsLoading(false);
        };

        if (delay > 0) {
          setTimeout(process, delay);
        } else {
          process();
        }
      } catch (err) {
        console.error(err);
        setIsLoading(false);
      }
    };

    useEffect(() => {
      fetchCourses(1, true, searchTerm);
      setPage(1);
    }, []);

    useEffect(() => {
      if (page > 1) fetchCourses(page, false, searchTerm);
    }, [page]);

    const lastCourseRef = useCallback(node => {
      if (isLoading) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting && hasMore) {
          setPage(prev => prev + 1);
        }
      });
      if (node) observer.current.observe(node);
    }, [isLoading, hasMore]);

    const handleCourseClick = (course) => {
      setClickedCourseId(course.id);
      const encoded = encodeData({ id: course.id }); // Only encode the course ID
      console.log("Encoded ID ------------", encoded);
    
      setTimeout(() => {
        navigate(`/user/courses/courseDetails/${encodeURIComponent(encoded)}`);
      }, 400);
    };
    
    

    const handleSearchChange = (e) => {
      const value = e.target.value;
      setSearchTerm(value);
      if (searchTimeout.current) clearTimeout(searchTimeout.current);
      searchTimeout.current = setTimeout(() => {
        setCourses([]);
        setPage(1);
        fetchCourses(1, true, value);
      }, 500);
    };

    return (
      <div className="course-tab-content">
        {/* Search Bar */}
        <div className="row mt-2 mb-2">
          <div className="col-12 col-md-4 ">
            <div className="seach-control position-relative">
              <input
                type="text"
                className="form-control search-input"
                placeholder="Search courses..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
              {isLoading && (
                <div
                  className="spinner-border text-primary position-absolute"
                  style={{
                    width: '1rem',
                    height: '1rem',
                    right: '10px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                  }}
                  role="status"
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* No Data */}
        {courses.length === 0 && !isLoading && (
          <div className="d-flex justify-content-center align-items-center w-100" style={{ minHeight: '300px' }}>
            <NoData message="No courses found." />
          </div>
        )}

        {/* Courses Grid */}
        <div className="row">
          {courses.map((course, index) => (
            <div
              key={course.id}
              ref={index === courses.length - 1 ? lastCourseRef : null}
              className="col-md-6 col-lg-3 mb-2"
            >
              <div className="course-card">
                <div className="course-image">
                  <img src={course.image} alt={course.title} className="img-fluid" />
                </div>
                <div className="course-details">
                  <h5 className="course-title">{course.title}</h5>
                  <p className="course-description">{course.description}</p>

                  <div className="course-meta-info">
                    <div className="meta-row d-flex justify-content-between">
                      <div className="views-count">
                        <Icon icon="mdi:eye-outline" className="meta-icon" />
                        <span>{course.enrolled}</span>
                      </div>
                      <div className="rating-stars-container">
                        <Icon icon="mdi:star" className="star-icon" />
                        <span className="rating-value">{course.rating}</span>
                      </div>
                    </div>

                    <div className="meta-row d-flex justify-content-between">
                      <div className="course-duration">
                        <Icon icon="mdi:clock-outline" className="meta-icon" />
                        <span>{course.modules} module{course.modules !== 1 ? 's' : ''}</span>
                      </div>
                      <div className="course-duration">
                        <Icon icon="mdi:signal-cellular-outline" className="meta-icon" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                  </div>

                  <div className="course-footer">
                    <div className={`course-price ${course.price === 'Free' ? 'free-price' : 'paid-price'}`}>
                      {course.price === 'Free' ? 'Free' : (
                        <>
                          <span className="price-icon">{getCurrencySymbol(course.currency)}</span>
                          {course.price}
                        </>
                      )}
                    </div>
                    <button
                      onClick={() => handleCourseClick(course)}
                      className={`watch-now-btn w-50 btn btn-primary`}
                      disabled={clickedCourseId === course.id}
                    >
                      {clickedCourseId === course.id ? (
                        <div className="spinner-border spinner-border-sm text-light" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      ) : (
                        <>
                          <Icon
                            icon={course.course_type?.toLowerCase() === 'free' ? "mdi:play-circle" : "mdi:lock"}
                            className="btn-icon"
                          />
                          {course.course_type?.toLowerCase() === 'free' ? 'Watch Now' : 'Enroll Now'}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          {isLoading && <p className="text-center mt-2">Loading more courses...</p>}
        </div>
      </div>
    );
  }

  export default CourseTab;
