[{"C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx": "4", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx": "5", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx": "6", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx": "7", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx": "8", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx": "9", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx": "10", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx": "11", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx": "12", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx": "13", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx": "14", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx": "15", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx": "16", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx": "17", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx": "18", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx": "19", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx": "20", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx": "21", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx": "22", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx": "23", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx": "24", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx": "25", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx": "26", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx": "27", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx": "28", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx": "29", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx": "30", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx": "31", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx": "32", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx": "33", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx": "34", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx": "35", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx": "36", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx": "37", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx": "38", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx": "39", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx": "40", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx": "41", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx": "42", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx": "43", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx": "44", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx": "45", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx": "46", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx": "47", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx": "48", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx": "49", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx": "50", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx": "51", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx": "52", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx": "53", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx": "54", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx": "55", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx": "56", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx": "57", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx": "58", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx": "59", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx": "60", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx": "61", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx": "62", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx": "63", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx": "64", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx": "65", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx": "66", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx": "67", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx": "68", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx": "69", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx": "70", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx": "71", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx": "72", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx": "73", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx": "74", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx": "75", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx": "76", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx": "77", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js": "78", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js": "79", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js": "80", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx": "81", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js": "82", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx": "83", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx": "84", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx": "85", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx": "86", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx": "87", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx": "88", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx": "89", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx": "90", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx": "91", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx": "92", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx": "93", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx": "94", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js": "95", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx": "96", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx": "97", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx": "98", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx": "99", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx": "100", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx": "101", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx": "102", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx": "103", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx": "104", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx": "105", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx": "106", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx": "107", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js": "108", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx": "109", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx": "110", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx": "111", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx": "112", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx": "113", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx": "114", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js": "115", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx": "116", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx": "117", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx": "118", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx": "119", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx": "120", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx": "121", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx": "122", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx": "123", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx": "124", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx": "125", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx": "126", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx": "127", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx": "128", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx": "129", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx": "130", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx": "131", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx": "132", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx": "133", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx": "134", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx": "135", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js": "136", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx": "137", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx": "138", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx": "139", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx": "140", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx": "141", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx": "142", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx": "143", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx": "144", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx": "145", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx": "146", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx": "147", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js": "148", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx": "149", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx": "150", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx": "151", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx": "152", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx": "153", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js": "154", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx": "155", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx": "156", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx": "157", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx": "158", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js": "159", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx": "160", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx": "161", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx": "162", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx": "163"}, {"size": 552, "mtime": *************, "results": "164", "hashOfConfig": "165"}, {"size": 375, "mtime": *************, "results": "166", "hashOfConfig": "165"}, {"size": 1633, "mtime": *************, "results": "167", "hashOfConfig": "165"}, {"size": 7498, "mtime": *************, "results": "168", "hashOfConfig": "165"}, {"size": 4546, "mtime": 1753890072773, "results": "169", "hashOfConfig": "165"}, {"size": 1522, "mtime": 1752921584997, "results": "170", "hashOfConfig": "165"}, {"size": 2212, "mtime": 1751054548000, "results": "171", "hashOfConfig": "165"}, {"size": 2266, "mtime": 1751054540000, "results": "172", "hashOfConfig": "165"}, {"size": 8314, "mtime": 1753384191084, "results": "173", "hashOfConfig": "165"}, {"size": 5573, "mtime": 1753383910530, "results": "174", "hashOfConfig": "165"}, {"size": 14993, "mtime": 1753383833576, "results": "175", "hashOfConfig": "165"}, {"size": 5376, "mtime": 1753383939567, "results": "176", "hashOfConfig": "165"}, {"size": 4637, "mtime": 1753383886944, "results": "177", "hashOfConfig": "165"}, {"size": 9424, "mtime": 1753383999422, "results": "178", "hashOfConfig": "165"}, {"size": 882, "mtime": 1750498842000, "results": "179", "hashOfConfig": "165"}, {"size": 887, "mtime": 1750500968000, "results": "180", "hashOfConfig": "165"}, {"size": 23107, "mtime": 1753190819653, "results": "181", "hashOfConfig": "165"}, {"size": 64676, "mtime": 1753170429987, "results": "182", "hashOfConfig": "165"}, {"size": 53481, "mtime": 1752484548147, "results": "183", "hashOfConfig": "165"}, {"size": 65216, "mtime": 1752490834024, "results": "184", "hashOfConfig": "165"}, {"size": 4643, "mtime": 1752409801600, "results": "185", "hashOfConfig": "165"}, {"size": 4216, "mtime": 1752409801605, "results": "186", "hashOfConfig": "165"}, {"size": 40073, "mtime": 1752409801612, "results": "187", "hashOfConfig": "165"}, {"size": 36784, "mtime": 1752409801613, "results": "188", "hashOfConfig": "165"}, {"size": 51452, "mtime": 1753276336843, "results": "189", "hashOfConfig": "165"}, {"size": 12687, "mtime": 1751710504000, "results": "190", "hashOfConfig": "165"}, {"size": 169, "mtime": 1752677915041, "results": "191", "hashOfConfig": "165"}, {"size": 9308, "mtime": 1751767716000, "results": "192", "hashOfConfig": "165"}, {"size": 77456, "mtime": 1752409801619, "results": "193", "hashOfConfig": "165"}, {"size": 22490, "mtime": 1752409801616, "results": "194", "hashOfConfig": "165"}, {"size": 26511, "mtime": 1752409801617, "results": "195", "hashOfConfig": "165"}, {"size": 5517, "mtime": 1752409801623, "results": "196", "hashOfConfig": "165"}, {"size": 32410, "mtime": 1752409801620, "results": "197", "hashOfConfig": "165"}, {"size": 44049, "mtime": 1753871529550, "results": "198", "hashOfConfig": "165"}, {"size": 100330, "mtime": 1752409801626, "results": "199", "hashOfConfig": "165"}, {"size": 7690, "mtime": 1751768538000, "results": "200", "hashOfConfig": "165"}, {"size": 15453, "mtime": 1751722792000, "results": "201", "hashOfConfig": "165"}, {"size": 54929, "mtime": 1752409801636, "results": "202", "hashOfConfig": "165"}, {"size": 36510, "mtime": 1752920830735, "results": "203", "hashOfConfig": "165"}, {"size": 3661, "mtime": 1752338196000, "results": "204", "hashOfConfig": "165"}, {"size": 54017, "mtime": 1752829580976, "results": "205", "hashOfConfig": "165"}, {"size": 11659, "mtime": 1752411851791, "results": "206", "hashOfConfig": "165"}, {"size": 1670, "mtime": 1752409801634, "results": "207", "hashOfConfig": "165"}, {"size": 5099, "mtime": 1752836652921, "results": "208", "hashOfConfig": "165"}, {"size": 10347, "mtime": 1753280697739, "results": "209", "hashOfConfig": "165"}, {"size": 5709, "mtime": 1752409801648, "results": "210", "hashOfConfig": "165"}, {"size": 48841, "mtime": 1752409801651, "results": "211", "hashOfConfig": "165"}, {"size": 11615, "mtime": 1752097860000, "results": "212", "hashOfConfig": "165"}, {"size": 27087, "mtime": 1752409801621, "results": "213", "hashOfConfig": "165"}, {"size": 9781, "mtime": 1753216567180, "results": "214", "hashOfConfig": "165"}, {"size": 1734, "mtime": 1751059658000, "results": "215", "hashOfConfig": "165"}, {"size": 2266, "mtime": 1753216737273, "results": "216", "hashOfConfig": "165"}, {"size": 8117, "mtime": 1752409801668, "results": "217", "hashOfConfig": "165"}, {"size": 10168, "mtime": 1751782620000, "results": "218", "hashOfConfig": "165"}, {"size": 27198, "mtime": 1753879087755, "results": "219", "hashOfConfig": "165"}, {"size": 4076, "mtime": 1751035930000, "results": "220", "hashOfConfig": "165"}, {"size": 4662, "mtime": 1752409801676, "results": "221", "hashOfConfig": "165"}, {"size": 4567, "mtime": 1753879568970, "results": "222", "hashOfConfig": "165"}, {"size": 40890, "mtime": 1752409801601, "results": "223", "hashOfConfig": "165"}, {"size": 49748, "mtime": 1752409801602, "results": "224", "hashOfConfig": "165"}, {"size": 29513, "mtime": 1752409801607, "results": "225", "hashOfConfig": "165"}, {"size": 20882, "mtime": 1752409801608, "results": "226", "hashOfConfig": "165"}, {"size": 7771, "mtime": 1751767656000, "results": "227", "hashOfConfig": "165"}, {"size": 7751, "mtime": 1751767830000, "results": "228", "hashOfConfig": "165"}, {"size": 8583, "mtime": 1751710504000, "results": "229", "hashOfConfig": "165"}, {"size": 9357, "mtime": 1753007074236, "results": "230", "hashOfConfig": "165"}, {"size": 9025, "mtime": 1750975280000, "results": "231", "hashOfConfig": "165"}, {"size": 7922, "mtime": *************, "results": "232", "hashOfConfig": "165"}, {"size": 23471, "mtime": 1752409801655, "results": "233", "hashOfConfig": "165"}, {"size": 5224, "mtime": 1750548386000, "results": "234", "hashOfConfig": "165"}, {"size": 22425, "mtime": 1752677199172, "results": "235", "hashOfConfig": "165"}, {"size": 4066, "mtime": 1750548608000, "results": "236", "hashOfConfig": "165"}, {"size": 3862, "mtime": *************, "results": "237", "hashOfConfig": "165"}, {"size": 5099, "mtime": 1752836952067, "results": "238", "hashOfConfig": "165"}, {"size": 10555, "mtime": 1753260541912, "results": "239", "hashOfConfig": "165"}, {"size": 14135, "mtime": 1753359213497, "results": "240", "hashOfConfig": "165"}, {"size": 10347, "mtime": 1753280688735, "results": "241", "hashOfConfig": "165"}, {"size": 13237, "mtime": 1750509658000, "results": "242", "hashOfConfig": "165"}, {"size": 760, "mtime": 1750582154000, "results": "243", "hashOfConfig": "165"}, {"size": 1464, "mtime": 1753081064033, "results": "244", "hashOfConfig": "165"}, {"size": 61762, "mtime": 1752931709150, "results": "245", "hashOfConfig": "165"}, {"size": 27929, "mtime": 1753099878281, "results": "246", "hashOfConfig": "165"}, {"size": 8756, "mtime": 1753280085442, "results": "247", "hashOfConfig": "165"}, {"size": 0, "mtime": 1751054256000, "results": "248", "hashOfConfig": "165"}, {"size": 21900, "mtime": 1752409801578, "results": "249", "hashOfConfig": "165"}, {"size": 409, "mtime": *************, "results": "250", "hashOfConfig": "165"}, {"size": 10020, "mtime": 1753861868097, "results": "251", "hashOfConfig": "165"}, {"size": 8024, "mtime": 1753280076928, "results": "252", "hashOfConfig": "165"}, {"size": 11878, "mtime": 1753384103074, "results": "253", "hashOfConfig": "165"}, {"size": 13068, "mtime": 1753884508765, "results": "254", "hashOfConfig": "165"}, {"size": 488, "mtime": 1751579418000, "results": "255", "hashOfConfig": "165"}, {"size": 506, "mtime": 1751579400000, "results": "256", "hashOfConfig": "165"}, {"size": 1452, "mtime": 1751437768000, "results": "257", "hashOfConfig": "165"}, {"size": 23613, "mtime": 1752409801630, "results": "258", "hashOfConfig": "165"}, {"size": 10755, "mtime": 1753856767160, "results": "259", "hashOfConfig": "165"}, {"size": 25665, "mtime": 1752409801627, "results": "260", "hashOfConfig": "165"}, {"size": 19281, "mtime": 1752409801594, "results": "261", "hashOfConfig": "165"}, {"size": 16387, "mtime": 1752409801614, "results": "262", "hashOfConfig": "165"}, {"size": 30692, "mtime": 1752409801629, "results": "263", "hashOfConfig": "165"}, {"size": 20468, "mtime": 1752409801591, "results": "264", "hashOfConfig": "165"}, {"size": 23434, "mtime": 1752409801593, "results": "265", "hashOfConfig": "165"}, {"size": 3508, "mtime": 1752049504000, "results": "266", "hashOfConfig": "165"}, {"size": 2614, "mtime": 1751826792000, "results": "267", "hashOfConfig": "165"}, {"size": 1923, "mtime": 1751826502000, "results": "268", "hashOfConfig": "165"}, {"size": 50190, "mtime": 1752409801633, "results": "269", "hashOfConfig": "165"}, {"size": 1927, "mtime": 1751826456000, "results": "270", "hashOfConfig": "165"}, {"size": 23878, "mtime": 1752409801643, "results": "271", "hashOfConfig": "165"}, {"size": 1708, "mtime": 1752408694807, "results": "272", "hashOfConfig": "165"}, {"size": 7643, "mtime": 1750763974000, "results": "273", "hashOfConfig": "165"}, {"size": 23256, "mtime": 1752409801645, "results": "274", "hashOfConfig": "165"}, {"size": 21217, "mtime": 1752409801647, "results": "275", "hashOfConfig": "165"}, {"size": 22643, "mtime": 1752409801644, "results": "276", "hashOfConfig": "165"}, {"size": 2997, "mtime": 1751882506000, "results": "277", "hashOfConfig": "165"}, {"size": 16290, "mtime": 1752409801649, "results": "278", "hashOfConfig": "165"}, {"size": 388, "mtime": 1751774832000, "results": "279", "hashOfConfig": "165"}, {"size": 9439, "mtime": 1751882686000, "results": "280", "hashOfConfig": "165"}, {"size": 8837, "mtime": 1751887002000, "results": "281", "hashOfConfig": "165"}, {"size": 1896, "mtime": 1751609914000, "results": "282", "hashOfConfig": "165"}, {"size": 4484, "mtime": 1751882686000, "results": "283", "hashOfConfig": "165"}, {"size": 4450, "mtime": 1751482138000, "results": "284", "hashOfConfig": "165"}, {"size": 16839, "mtime": 1752683212096, "results": "285", "hashOfConfig": "165"}, {"size": 5743, "mtime": 1750625032000, "results": "286", "hashOfConfig": "165"}, {"size": 5240, "mtime": 1752054098000, "results": "287", "hashOfConfig": "165"}, {"size": 12656, "mtime": 1750906260000, "results": "288", "hashOfConfig": "165"}, {"size": 49712, "mtime": 1751053448000, "results": "289", "hashOfConfig": "165"}, {"size": 7643, "mtime": 1750763974000, "results": "290", "hashOfConfig": "165"}, {"size": 9177, "mtime": 1753875005973, "results": "291", "hashOfConfig": "165"}, {"size": 18218, "mtime": 1752409801667, "results": "292", "hashOfConfig": "165"}, {"size": 9495, "mtime": 1752435437340, "results": "293", "hashOfConfig": "165"}, {"size": 6809, "mtime": 1751608336000, "results": "294", "hashOfConfig": "165"}, {"size": 13304, "mtime": 1752435292539, "results": "295", "hashOfConfig": "165"}, {"size": 5931, "mtime": 1750543146000, "results": "296", "hashOfConfig": "165"}, {"size": 430, "mtime": 1750591742000, "results": "297", "hashOfConfig": "165"}, {"size": 11350, "mtime": 1751037418000, "results": "298", "hashOfConfig": "165"}, {"size": 5807, "mtime": 1750533816000, "results": "299", "hashOfConfig": "165"}, {"size": 7407, "mtime": 1752767163918, "results": "300", "hashOfConfig": "165"}, {"size": 3831, "mtime": *************, "results": "301", "hashOfConfig": "165"}, {"size": 8425, "mtime": 1751813544000, "results": "302", "hashOfConfig": "165"}, {"size": 1406, "mtime": 1750548574000, "results": "303", "hashOfConfig": "165"}, {"size": 18082, "mtime": 1752474860957, "results": "304", "hashOfConfig": "165"}, {"size": 10944, "mtime": 1752484370321, "results": "305", "hashOfConfig": "165"}, {"size": 19638, "mtime": 1753276143523, "results": "306", "hashOfConfig": "165"}, {"size": 17989, "mtime": 1752697222607, "results": "307", "hashOfConfig": "165"}, {"size": 8503, "mtime": 1752680006516, "results": "308", "hashOfConfig": "165"}, {"size": 7673, "mtime": 1752680102177, "results": "309", "hashOfConfig": "165"}, {"size": 626, "mtime": 1751536590000, "results": "310", "hashOfConfig": "165"}, {"size": 18515, "mtime": 1752683186304, "results": "311", "hashOfConfig": "165"}, {"size": 1686, "mtime": 1752409801587, "results": "312", "hashOfConfig": "165"}, {"size": 2272, "mtime": 1752409801638, "results": "313", "hashOfConfig": "165"}, {"size": 6037, "mtime": 1752409801588, "results": "314", "hashOfConfig": "165"}, {"size": 52979, "mtime": 1752685608016, "results": "315", "hashOfConfig": "165"}, {"size": 26870, "mtime": 1752827191316, "results": "316", "hashOfConfig": "165"}, {"size": 16901, "mtime": 1752409801670, "results": "317", "hashOfConfig": "165"}, {"size": 925, "mtime": 1752409801586, "results": "318", "hashOfConfig": "165"}, {"size": 52070, "mtime": 1752697034820, "results": "319", "hashOfConfig": "165"}, {"size": 184, "mtime": 1752749250332, "results": "320", "hashOfConfig": "165"}, {"size": 6226, "mtime": 1753383969771, "results": "321", "hashOfConfig": "165"}, {"size": 8705, "mtime": 1753278652965, "results": "322", "hashOfConfig": "165"}, {"size": 1258, "mtime": 1753386028029, "results": "323", "hashOfConfig": "165"}, {"size": 7621, "mtime": 1753885536281, "results": "324", "hashOfConfig": "165"}, {"size": 5672, "mtime": 1753885538738, "results": "325", "hashOfConfig": "165"}, {"size": 11695, "mtime": 1753890036950, "results": "326", "hashOfConfig": "165"}, {"size": 11352, "mtime": 1753890023205, "results": "327", "hashOfConfig": "165"}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3wgbg", {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js", ["817", "818", "819"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx", ["820"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx", ["821"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx", ["822", "823"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx", ["824", "825"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx", ["826"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx", ["827"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx", ["828", "829"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx", ["830", "831", "832", "833"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx", ["834", "835", "836", "837"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx", ["838", "839", "840"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx", ["841", "842"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx", ["843", "844", "845"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx", ["846", "847"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx", ["848", "849", "850", "851"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx", ["852"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx", ["853", "854"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx", ["855", "856", "857", "858", "859", "860"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx", ["861", "862", "863"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx", ["864", "865", "866", "867"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx", ["868"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx", ["869", "870", "871", "872"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx", ["873"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx", ["874", "875"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx", ["876", "877"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx", ["878", "879", "880", "881", "882", "883"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx", ["884"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx", ["885", "886"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx", ["887"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx", ["888", "889", "890"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx", ["891", "892", "893", "894", "895"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx", ["896", "897"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx", ["898", "899", "900"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx", ["901"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx", ["902"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx", ["903", "904", "905", "906", "907"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx", ["908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx", ["925", "926", "927"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx", ["928", "929"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx", ["930"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx", ["931", "932", "933"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx", ["934", "935", "936"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx", ["937", "938", "939"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx", ["940"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx", ["941"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx", ["942", "943"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx", ["944", "945"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx", ["946", "947"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx", ["948"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx", ["949", "950"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx", ["951"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx", ["952"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx", ["953", "954", "955"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx", ["956", "957", "958"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx", ["959", "960", "961", "962", "963", "964"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx", ["965", "966"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx", ["967"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx", ["968", "969"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx", ["970", "971"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx", ["972", "973"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx", ["974"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx", ["975", "976"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx", ["977"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx", ["978"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx", ["979", "980", "981", "982", "983", "984", "985"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx", ["986"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx", ["987"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx", ["988"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx", ["989"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx", ["990"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx", ["991"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx", ["992", "993", "994", "995"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx", ["996"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx", ["997"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx", ["998", "999", "1000", "1001", "1002", "1003", "1004"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx", ["1005", "1006"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx", ["1007", "1008"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx", ["1009"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx", ["1010", "1011"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx", ["1012", "1013", "1014", "1015"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx", ["1016"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx", ["1017", "1018"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx", ["1019", "1020"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx", ["1021"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx", ["1022", "1023"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx", ["1024", "1025", "1026", "1027"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx", ["1028", "1029"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx", ["1030"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx", ["1031", "1032"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx", ["1033", "1034", "1035", "1036"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx", ["1037", "1038", "1039", "1040", "1041"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx", ["1042"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx", ["1043", "1044"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx", ["1045", "1046", "1047", "1048"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx", ["1049"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx", ["1050"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx", ["1051"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx", ["1052", "1053"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx", ["1054", "1055"], [], {"ruleId": "1056", "severity": 1, "message": "1057", "line": 1, "column": 25, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 31}, {"ruleId": "1056", "severity": 1, "message": "1060", "line": 1, "column": 33, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 38}, {"ruleId": "1056", "severity": 1, "message": "1061", "line": 1, "column": 40, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 48}, {"ruleId": "1056", "severity": 1, "message": "1062", "line": 3, "column": 17, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1062", "line": 1, "column": 17, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1063", "line": 2, "column": 18, "nodeType": "1058", "messageId": "1059", "endLine": 2, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1064", "line": 5, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1063", "line": 2, "column": 18, "nodeType": "1058", "messageId": "1059", "endLine": 2, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1064", "line": 5, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1065", "line": 7, "column": 20, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 30}, {"ruleId": "1056", "severity": 1, "message": "1066", "line": 14, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 14, "endColumn": 24}, {"ruleId": "1056", "severity": 1, "message": "1067", "line": 34, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 34, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1068", "line": 34, "column": 23, "nodeType": "1058", "messageId": "1059", "endLine": 34, "endColumn": 37}, {"ruleId": "1056", "severity": 1, "message": "1069", "line": 24, "column": 25, "nodeType": "1058", "messageId": "1059", "endLine": 24, "endColumn": 41}, {"ruleId": "1056", "severity": 1, "message": "1070", "line": 26, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 26, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1072", "line": 221, "column": 6, "nodeType": "1073", "endLine": 221, "endColumn": 8, "suggestions": "1074"}, {"ruleId": "1075", "severity": 1, "message": "1076", "line": 229, "column": 5, "nodeType": "1077", "messageId": "1078", "endLine": 260, "endColumn": 6}, {"ruleId": "1056", "severity": 1, "message": "1079", "line": 10, "column": 22, "nodeType": "1058", "messageId": "1059", "endLine": 10, "endColumn": 32}, {"ruleId": "1056", "severity": 1, "message": "1080", "line": 49, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 49, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1081", "line": 50, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 50, "endColumn": 24}, {"ruleId": "1071", "severity": 1, "message": "1082", "line": 110, "column": 8, "nodeType": "1073", "endLine": 110, "endColumn": 48, "suggestions": "1083"}, {"ruleId": "1071", "severity": 1, "message": "1084", "line": 94, "column": 6, "nodeType": "1073", "endLine": 94, "endColumn": 26, "suggestions": "1085"}, {"ruleId": "1056", "severity": 1, "message": "1086", "line": 237, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 237, "endColumn": 31}, {"ruleId": "1056", "severity": 1, "message": "1087", "line": 474, "column": 17, "nodeType": "1058", "messageId": "1059", "endLine": 474, "endColumn": 26}, {"ruleId": "1056", "severity": 1, "message": "1088", "line": 212, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 212, "endColumn": 27}, {"ruleId": "1056", "severity": 1, "message": "1089", "line": 229, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 229, "endColumn": 28}, {"ruleId": "1056", "severity": 1, "message": "1090", "line": 13, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 19}, {"ruleId": "1071", "severity": 1, "message": "1091", "line": 131, "column": 8, "nodeType": "1073", "endLine": 131, "endColumn": 67, "suggestions": "1092"}, {"ruleId": "1071", "severity": 1, "message": "1093", "line": 142, "column": 8, "nodeType": "1073", "endLine": 142, "endColumn": 39, "suggestions": "1094"}, {"ruleId": "1056", "severity": 1, "message": "1095", "line": 17, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 17, "endColumn": 17}, {"ruleId": "1071", "severity": 1, "message": "1096", "line": 60, "column": 8, "nodeType": "1073", "endLine": 60, "endColumn": 10, "suggestions": "1097"}, {"ruleId": "1071", "severity": 1, "message": "1098", "line": 79, "column": 6, "nodeType": "1073", "endLine": 79, "endColumn": 82, "suggestions": "1099"}, {"ruleId": "1056", "severity": 1, "message": "1100", "line": 256, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 256, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1100", "line": 278, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 278, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1101", "line": 467, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 467, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1102", "line": 23, "column": 8, "nodeType": "1073", "endLine": 23, "endColumn": 25, "suggestions": "1103"}, {"ruleId": "1056", "severity": 1, "message": "1104", "line": 4, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1105", "line": 20, "column": 6, "nodeType": "1073", "endLine": 20, "endColumn": 44, "suggestions": "1106"}, {"ruleId": "1056", "severity": 1, "message": "1107", "line": 1, "column": 38, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 49}, {"ruleId": "1056", "severity": 1, "message": "1108", "line": 10, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 10, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1109", "line": 52, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 52, "endColumn": 26}, {"ruleId": "1056", "severity": 1, "message": "1110", "line": 267, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 267, "endColumn": 40}, {"ruleId": "1056", "severity": 1, "message": "1111", "line": 704, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 704, "endColumn": 32}, {"ruleId": "1056", "severity": 1, "message": "1112", "line": 740, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 740, "endColumn": 30}, {"ruleId": "1056", "severity": 1, "message": "1090", "line": 19, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 19, "endColumn": 19}, {"ruleId": "1071", "severity": 1, "message": "1113", "line": 163, "column": 8, "nodeType": "1073", "endLine": 163, "endColumn": 26, "suggestions": "1114"}, {"ruleId": "1056", "severity": 1, "message": "1115", "line": 183, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 183, "endColumn": 31}, {"ruleId": "1056", "severity": 1, "message": "1116", "line": 6, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1117", "line": 15, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 17}, {"ruleId": "1071", "severity": 1, "message": "1118", "line": 80, "column": 6, "nodeType": "1073", "endLine": 80, "endColumn": 8, "suggestions": "1119"}, {"ruleId": "1071", "severity": 1, "message": "1118", "line": 88, "column": 6, "nodeType": "1073", "endLine": 88, "endColumn": 14, "suggestions": "1120"}, {"ruleId": "1056", "severity": 1, "message": "1121", "line": 13, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 26}, {"ruleId": "1056", "severity": 1, "message": "1122", "line": 12, "column": 7, "nodeType": "1058", "messageId": "1059", "endLine": 12, "endColumn": 36}, {"ruleId": "1071", "severity": 1, "message": "1123", "line": 109, "column": 8, "nodeType": "1073", "endLine": 109, "endColumn": 10, "suggestions": "1124"}, {"ruleId": "1075", "severity": 1, "message": "1076", "line": 170, "column": 21, "nodeType": "1077", "messageId": "1078", "endLine": 183, "endColumn": 22}, {"ruleId": "1075", "severity": 1, "message": "1076", "line": 477, "column": 9, "nodeType": "1077", "messageId": "1078", "endLine": 556, "endColumn": 10}, {"ruleId": "1071", "severity": 1, "message": "1125", "line": 20, "column": 6, "nodeType": "1073", "endLine": 20, "endColumn": 40, "suggestions": "1126"}, {"ruleId": "1056", "severity": 1, "message": "1127", "line": 5, "column": 22, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 32}, {"ruleId": "1056", "severity": 1, "message": "1128", "line": 45, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 45, "endColumn": 20}, {"ruleId": "1056", "severity": 1, "message": "1129", "line": 5, "column": 98, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 117}, {"ruleId": "1071", "severity": 1, "message": "1130", "line": 351, "column": 6, "nodeType": "1073", "endLine": 351, "endColumn": 20, "suggestions": "1131"}, {"ruleId": "1056", "severity": 1, "message": "1132", "line": 26, "column": 18, "nodeType": "1058", "messageId": "1059", "endLine": 26, "endColumn": 25}, {"ruleId": "1056", "severity": 1, "message": "1133", "line": 27, "column": 19, "nodeType": "1058", "messageId": "1059", "endLine": 27, "endColumn": 27}, {"ruleId": "1056", "severity": 1, "message": "1134", "line": 42, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 42, "endColumn": 27}, {"ruleId": "1071", "severity": 1, "message": "1135", "line": 66, "column": 8, "nodeType": "1073", "endLine": 66, "endColumn": 21, "suggestions": "1136"}, {"ruleId": "1056", "severity": 1, "message": "1137", "line": 268, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 268, "endColumn": 33}, {"ruleId": "1056", "severity": 1, "message": "1138", "line": 282, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 282, "endColumn": 24}, {"ruleId": "1056", "severity": 1, "message": "1139", "line": 14, "column": 5, "nodeType": "1058", "messageId": "1059", "endLine": 14, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 9, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 9, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1072", "line": 90, "column": 6, "nodeType": "1073", "endLine": 90, "endColumn": 8, "suggestions": "1141"}, {"ruleId": "1056", "severity": 1, "message": "1142", "line": 43, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 43, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1143", "line": 22, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 22, "endColumn": 20}, {"ruleId": "1071", "severity": 1, "message": "1144", "line": 94, "column": 6, "nodeType": "1073", "endLine": 94, "endColumn": 27, "suggestions": "1145"}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 102, "column": 6, "nodeType": "1073", "endLine": 102, "endColumn": 27, "suggestions": "1147"}, {"ruleId": "1056", "severity": 1, "message": "1148", "line": 25, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 25, "endColumn": 25}, {"ruleId": "1056", "severity": 1, "message": "1149", "line": 25, "column": 27, "nodeType": "1058", "messageId": "1059", "endLine": 25, "endColumn": 45}, {"ruleId": "1056", "severity": 1, "message": "1150", "line": 426, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 426, "endColumn": 25}, {"ruleId": "1056", "severity": 1, "message": "1151", "line": 456, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 456, "endColumn": 29}, {"ruleId": "1071", "severity": 1, "message": "1152", "line": 483, "column": 6, "nodeType": "1073", "endLine": 483, "endColumn": 76, "suggestions": "1153"}, {"ruleId": "1056", "severity": 1, "message": "1154", "line": 40, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 40, "endColumn": 17}, {"ruleId": "1071", "severity": 1, "message": "1155", "line": 80, "column": 6, "nodeType": "1073", "endLine": 80, "endColumn": 17, "suggestions": "1156"}, {"ruleId": "1056", "severity": 1, "message": "1157", "line": 5, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 16}, {"ruleId": "1056", "severity": 1, "message": "1158", "line": 11, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 11, "endColumn": 28}, {"ruleId": "1056", "severity": 1, "message": "1159", "line": 11, "column": 30, "nodeType": "1058", "messageId": "1059", "endLine": 11, "endColumn": 51}, {"ruleId": "1056", "severity": 1, "message": "1160", "line": 5, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 24}, {"ruleId": "1071", "severity": 1, "message": "1161", "line": 15, "column": 6, "nodeType": "1073", "endLine": 15, "endColumn": 20, "suggestions": "1162"}, {"ruleId": "1056", "severity": 1, "message": "1163", "line": 20, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 20, "endColumn": 24}, {"ruleId": "1056", "severity": 1, "message": "1164", "line": 20, "column": 26, "nodeType": "1058", "messageId": "1059", "endLine": 20, "endColumn": 43}, {"ruleId": "1071", "severity": 1, "message": "1165", "line": 33, "column": 6, "nodeType": "1073", "endLine": 33, "endColumn": 16, "suggestions": "1166"}, {"ruleId": "1071", "severity": 1, "message": "1167", "line": 44, "column": 6, "nodeType": "1073", "endLine": 44, "endColumn": 18, "suggestions": "1168"}, {"ruleId": "1056", "severity": 1, "message": "1169", "line": 185, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 185, "endColumn": 35}, {"ruleId": "1056", "severity": 1, "message": "1170", "line": 3, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1171", "line": 3, "column": 51, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 61}, {"ruleId": "1056", "severity": 1, "message": "1172", "line": 4, "column": 3, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 12}, {"ruleId": "1056", "severity": 1, "message": "1173", "line": 4, "column": 14, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 24}, {"ruleId": "1056", "severity": 1, "message": "1174", "line": 4, "column": 54, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 60}, {"ruleId": "1071", "severity": 1, "message": "1175", "line": 89, "column": 6, "nodeType": "1073", "endLine": 89, "endColumn": 16, "suggestions": "1176"}, {"ruleId": "1056", "severity": 1, "message": "1177", "line": 151, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 151, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1178", "line": 184, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 184, "endColumn": 26}, {"ruleId": "1056", "severity": 1, "message": "1179", "line": 240, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 240, "endColumn": 25}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 573, "column": 54, "nodeType": "1182", "messageId": "1183", "endLine": 573, "endColumn": 56}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 573, "column": 143, "nodeType": "1182", "messageId": "1183", "endLine": 573, "endColumn": 145}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 577, "column": 54, "nodeType": "1182", "messageId": "1183", "endLine": 577, "endColumn": 56}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 577, "column": 148, "nodeType": "1182", "messageId": "1183", "endLine": 577, "endColumn": 150}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 581, "column": 54, "nodeType": "1182", "messageId": "1183", "endLine": 581, "endColumn": 56}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 581, "column": 146, "nodeType": "1182", "messageId": "1183", "endLine": 581, "endColumn": 148}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 585, "column": 54, "nodeType": "1182", "messageId": "1183", "endLine": 585, "endColumn": 56}, {"ruleId": "1180", "severity": 1, "message": "1181", "line": 585, "column": 144, "nodeType": "1182", "messageId": "1183", "endLine": 585, "endColumn": 146}, {"ruleId": "1056", "severity": 1, "message": "1184", "line": 5, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 25}, {"ruleId": "1056", "severity": 1, "message": "1185", "line": 12, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 12, "endColumn": 18}, {"ruleId": "1071", "severity": 1, "message": "1186", "line": 55, "column": 6, "nodeType": "1073", "endLine": 55, "endColumn": 8, "suggestions": "1187"}, {"ruleId": "1056", "severity": 1, "message": "1188", "line": 1, "column": 27, "nodeType": "1058", "messageId": "1059", "endLine": 1, "endColumn": 36}, {"ruleId": "1056", "severity": 1, "message": "1189", "line": 3, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 19}, {"ruleId": "1071", "severity": 1, "message": "1190", "line": 60, "column": 6, "nodeType": "1073", "endLine": 60, "endColumn": 29, "suggestions": "1191"}, {"ruleId": "1056", "severity": 1, "message": "1192", "line": 27, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 27, "endColumn": 28}, {"ruleId": "1056", "severity": 1, "message": "1193", "line": 28, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 28, "endColumn": 32}, {"ruleId": "1071", "severity": 1, "message": "1194", "line": 48, "column": 8, "nodeType": "1073", "endLine": 48, "endColumn": 63, "suggestions": "1195"}, {"ruleId": "1056", "severity": 1, "message": "1196", "line": 14, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 14, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1197", "line": 15, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 26}, {"ruleId": "1071", "severity": 1, "message": "1198", "line": 47, "column": 8, "nodeType": "1073", "endLine": 47, "endColumn": 88, "suggestions": "1199"}, {"ruleId": "1056", "severity": 1, "message": "1200", "line": 8, "column": 3, "nodeType": "1058", "messageId": "1059", "endLine": 8, "endColumn": 37}, {"ruleId": "1056", "severity": 1, "message": "1201", "line": 21, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 21, "endColumn": 27}, {"ruleId": "1056", "severity": 1, "message": "1142", "line": 217, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 217, "endColumn": 19}, {"ruleId": "1071", "severity": 1, "message": "1202", "line": 26, "column": 6, "nodeType": "1073", "endLine": 26, "endColumn": 48, "suggestions": "1203"}, {"ruleId": "1071", "severity": 1, "message": "1204", "line": 26, "column": 6, "nodeType": "1073", "endLine": 26, "endColumn": 48, "suggestions": "1205"}, {"ruleId": "1056", "severity": 1, "message": "1104", "line": 3, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1206", "line": 27, "column": 6, "nodeType": "1073", "endLine": 27, "endColumn": 62, "suggestions": "1207"}, {"ruleId": "1056", "severity": 1, "message": "1208", "line": 21, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 21, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1209", "line": 88, "column": 6, "nodeType": "1073", "endLine": 88, "endColumn": 40, "suggestions": "1210"}, {"ruleId": "1071", "severity": 1, "message": "1211", "line": 54, "column": 27, "nodeType": "1058", "endLine": 54, "endColumn": 38}, {"ruleId": "1071", "severity": 1, "message": "1212", "line": 86, "column": 6, "nodeType": "1073", "endLine": 86, "endColumn": 12, "suggestions": "1213"}, {"ruleId": "1056", "severity": 1, "message": "1214", "line": 5, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 7, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1215", "line": 107, "column": 8, "nodeType": "1073", "endLine": 107, "endColumn": 39, "suggestions": "1216"}, {"ruleId": "1071", "severity": 1, "message": "1130", "line": 74, "column": 8, "nodeType": "1073", "endLine": 74, "endColumn": 46, "suggestions": "1217"}, {"ruleId": "1056", "severity": 1, "message": "1142", "line": 43, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 43, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1218", "line": 5, "column": 57, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 77}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 6, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1219", "line": 30, "column": 6, "nodeType": "1073", "endLine": 30, "endColumn": 31, "suggestions": "1220"}, {"ruleId": "1056", "severity": 1, "message": "1143", "line": 22, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 22, "endColumn": 20}, {"ruleId": "1071", "severity": 1, "message": "1144", "line": 94, "column": 6, "nodeType": "1073", "endLine": 94, "endColumn": 27, "suggestions": "1221"}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 102, "column": 6, "nodeType": "1073", "endLine": 102, "endColumn": 27, "suggestions": "1222"}, {"ruleId": "1071", "severity": 1, "message": "1223", "line": 239, "column": 6, "nodeType": "1073", "endLine": 239, "endColumn": 49, "suggestions": "1224"}, {"ruleId": "1056", "severity": 1, "message": "1225", "line": 658, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 658, "endColumn": 26}, {"ruleId": "1056", "severity": 1, "message": "1226", "line": 709, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 709, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1227", "line": 835, "column": 19, "nodeType": "1058", "messageId": "1059", "endLine": 835, "endColumn": 29}, {"ruleId": "1056", "severity": 1, "message": "1228", "line": 838, "column": 19, "nodeType": "1058", "messageId": "1059", "endLine": 838, "endColumn": 32}, {"ruleId": "1229", "severity": 1, "message": "1230", "line": 917, "column": 27, "nodeType": "1231", "endLine": 922, "endColumn": 29}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 39, "column": 6, "nodeType": "1073", "endLine": 39, "endColumn": 8, "suggestions": "1232"}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 48, "column": 6, "nodeType": "1073", "endLine": 48, "endColumn": 8, "suggestions": "1233"}, {"ruleId": "1056", "severity": 1, "message": "1234", "line": 584, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 584, "endColumn": 24}, {"ruleId": "1056", "severity": 1, "message": "1235", "line": 12, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 12, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1236", "line": 12, "column": 21, "nodeType": "1058", "messageId": "1059", "endLine": 12, "endColumn": 33}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 35, "column": 6, "nodeType": "1073", "endLine": 35, "endColumn": 8, "suggestions": "1237"}, {"ruleId": "1071", "severity": 1, "message": "1146", "line": 44, "column": 6, "nodeType": "1073", "endLine": 44, "endColumn": 8, "suggestions": "1238"}, {"ruleId": "1056", "severity": 1, "message": "1235", "line": 15, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1236", "line": 15, "column": 21, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 33}, {"ruleId": "1056", "severity": 1, "message": "1239", "line": 149, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 149, "endColumn": 22}, {"ruleId": "1056", "severity": 1, "message": "1240", "line": 3, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 3, "endColumn": 15}, {"ruleId": "1056", "severity": 1, "message": "1095", "line": 15, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 17}, {"ruleId": "1056", "severity": 1, "message": "1079", "line": 7, "column": 22, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 32}, {"ruleId": "1056", "severity": 1, "message": "1104", "line": 2, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 2, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1241", "line": 6, "column": 39, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 51}, {"ruleId": "1056", "severity": 1, "message": "1242", "line": 13, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1243", "line": 13, "column": 25, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 39}, {"ruleId": "1056", "severity": 1, "message": "1244", "line": 124, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 124, "endColumn": 30}, {"ruleId": "1056", "severity": 1, "message": "1245", "line": 235, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 235, "endColumn": 28}, {"ruleId": "1056", "severity": 1, "message": "1246", "line": 247, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 247, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1247", "line": 255, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 255, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1248", "line": 35, "column": 6, "nodeType": "1073", "endLine": 35, "endColumn": 17, "suggestions": "1249"}, {"ruleId": "1071", "severity": 1, "message": "1250", "line": 45, "column": 6, "nodeType": "1073", "endLine": 45, "endColumn": 17, "suggestions": "1251"}, {"ruleId": "1071", "severity": 1, "message": "1252", "line": 38, "column": 6, "nodeType": "1073", "endLine": 38, "endColumn": 17, "suggestions": "1253"}, {"ruleId": "1071", "severity": 1, "message": "1254", "line": 38, "column": 6, "nodeType": "1073", "endLine": 38, "endColumn": 17, "suggestions": "1255"}, {"ruleId": "1071", "severity": 1, "message": "1256", "line": 35, "column": 6, "nodeType": "1073", "endLine": 35, "endColumn": 17, "suggestions": "1257"}, {"ruleId": "1056", "severity": 1, "message": "1240", "line": 6, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 15}, {"ruleId": "1056", "severity": 1, "message": "1258", "line": 7, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 20}, {"ruleId": "1071", "severity": 1, "message": "1259", "line": 32, "column": 6, "nodeType": "1073", "endLine": 32, "endColumn": 15, "suggestions": "1260"}, {"ruleId": "1056", "severity": 1, "message": "1261", "line": 87, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 87, "endColumn": 21}, {"ruleId": "1056", "severity": 1, "message": "1262", "line": 217, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 217, "endColumn": 26}, {"ruleId": "1071", "severity": 1, "message": "1263", "line": 10, "column": 6, "nodeType": "1073", "endLine": 10, "endColumn": 8, "suggestions": "1264"}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 4, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1241", "line": 6, "column": 39, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 51}, {"ruleId": "1056", "severity": 1, "message": "1242", "line": 13, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1243", "line": 13, "column": 25, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 39}, {"ruleId": "1056", "severity": 1, "message": "1244", "line": 124, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 124, "endColumn": 30}, {"ruleId": "1056", "severity": 1, "message": "1245", "line": 235, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 235, "endColumn": 28}, {"ruleId": "1056", "severity": 1, "message": "1246", "line": 247, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 247, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1247", "line": 255, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 255, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1265", "line": 86, "column": 8, "nodeType": "1073", "endLine": 86, "endColumn": 10, "suggestions": "1266"}, {"ruleId": "1071", "severity": 1, "message": "1265", "line": 90, "column": 8, "nodeType": "1073", "endLine": 90, "endColumn": 14, "suggestions": "1267"}, {"ruleId": "1071", "severity": 1, "message": "1268", "line": 33, "column": 6, "nodeType": "1073", "endLine": 33, "endColumn": 21, "suggestions": "1269"}, {"ruleId": "1056", "severity": 1, "message": "1270", "line": 173, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 173, "endColumn": 27}, {"ruleId": "1056", "severity": 1, "message": "1271", "line": 93, "column": 18, "nodeType": "1058", "messageId": "1059", "endLine": 93, "endColumn": 37}, {"ruleId": "1071", "severity": 1, "message": "1265", "line": 21, "column": 6, "nodeType": "1073", "endLine": 21, "endColumn": 8, "suggestions": "1272"}, {"ruleId": "1071", "severity": 1, "message": "1265", "line": 35, "column": 6, "nodeType": "1073", "endLine": 35, "endColumn": 32, "suggestions": "1273"}, {"ruleId": "1071", "severity": 1, "message": "1274", "line": 42, "column": 6, "nodeType": "1073", "endLine": 42, "endColumn": 23, "suggestions": "1275"}, {"ruleId": "1056", "severity": 1, "message": "1276", "line": 75, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 75, "endColumn": 26}, {"ruleId": "1071", "severity": 1, "message": "1277", "line": 163, "column": 6, "nodeType": "1073", "endLine": 163, "endColumn": 33, "suggestions": "1278"}, {"ruleId": "1071", "severity": 1, "message": "1186", "line": 187, "column": 6, "nodeType": "1073", "endLine": 187, "endColumn": 8, "suggestions": "1279"}, {"ruleId": "1280", "severity": 1, "message": "1281", "line": 77, "column": 13, "nodeType": "1231", "endLine": 77, "endColumn": 42}, {"ruleId": "1056", "severity": 1, "message": "1282", "line": 15, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 15, "endColumn": 24}, {"ruleId": "1071", "severity": 1, "message": "1283", "line": 50, "column": 6, "nodeType": "1073", "endLine": 50, "endColumn": 39, "suggestions": "1284"}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 4, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 4, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1285", "line": 30, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 30, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1142", "line": 21, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 21, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1286", "line": 67, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 67, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1142", "line": 80, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 80, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 6, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 6, "endColumn": 14}, {"ruleId": "1056", "severity": 1, "message": "1080", "line": 21, "column": 12, "nodeType": "1058", "messageId": "1059", "endLine": 21, "endColumn": 22}, {"ruleId": "1071", "severity": 1, "message": "1098", "line": 39, "column": 8, "nodeType": "1073", "endLine": 39, "endColumn": 85, "suggestions": "1287"}, {"ruleId": "1056", "severity": 1, "message": "1288", "line": 155, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 155, "endColumn": 23}, {"ruleId": "1071", "severity": 1, "message": "1289", "line": 63, "column": 6, "nodeType": "1073", "endLine": 63, "endColumn": 73, "suggestions": "1290"}, {"ruleId": "1280", "severity": 1, "message": "1291", "line": 184, "column": 13, "nodeType": "1231", "endLine": 188, "endColumn": 15}, {"ruleId": "1071", "severity": 1, "message": "1292", "line": 38, "column": 6, "nodeType": "1073", "endLine": 38, "endColumn": 25, "suggestions": "1293"}, {"ruleId": "1056", "severity": 1, "message": "1294", "line": 5, "column": 55, "nodeType": "1058", "messageId": "1059", "endLine": 5, "endColumn": 68}, {"ruleId": "1071", "severity": 1, "message": "1295", "line": 20, "column": 6, "nodeType": "1073", "endLine": 20, "endColumn": 16, "suggestions": "1296"}, {"ruleId": "1056", "severity": 1, "message": "1258", "line": 7, "column": 14, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 24}, {"ruleId": "1071", "severity": 1, "message": "1259", "line": 32, "column": 10, "nodeType": "1073", "endLine": 32, "endColumn": 19, "suggestions": "1297"}, {"ruleId": "1056", "severity": 1, "message": "1261", "line": 87, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 87, "endColumn": 25}, {"ruleId": "1056", "severity": 1, "message": "1262", "line": 217, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 217, "endColumn": 30}, {"ruleId": "1056", "severity": 1, "message": "1298", "line": 56, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 56, "endColumn": 20}, {"ruleId": "1056", "severity": 1, "message": "1299", "line": 89, "column": 25, "nodeType": "1058", "messageId": "1059", "endLine": 89, "endColumn": 41}, {"ruleId": "1056", "severity": 1, "message": "1300", "line": 93, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 93, "endColumn": 26}, {"ruleId": "1071", "severity": 1, "message": "1301", "line": 124, "column": 6, "nodeType": "1073", "endLine": 124, "endColumn": 60, "suggestions": "1302"}, {"ruleId": "1056", "severity": 1, "message": "1303", "line": 278, "column": 9, "nodeType": "1058", "messageId": "1059", "endLine": 278, "endColumn": 33}, {"ruleId": "1056", "severity": 1, "message": "1304", "line": 52, "column": 13, "nodeType": "1058", "messageId": "1059", "endLine": 52, "endColumn": 21}, {"ruleId": "1071", "severity": 1, "message": "1305", "line": 23, "column": 8, "nodeType": "1073", "endLine": 23, "endColumn": 10, "suggestions": "1306"}, {"ruleId": "1056", "severity": 1, "message": "1307", "line": 94, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 94, "endColumn": 23}, {"ruleId": "1056", "severity": 1, "message": "1143", "line": 18, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 18, "endColumn": 20}, {"ruleId": "1071", "severity": 1, "message": "1289", "line": 43, "column": 6, "nodeType": "1073", "endLine": 43, "endColumn": 61, "suggestions": "1308"}, {"ruleId": "1056", "severity": 1, "message": "1309", "line": 628, "column": 39, "nodeType": "1058", "messageId": "1059", "endLine": 628, "endColumn": 52}, {"ruleId": "1056", "severity": 1, "message": "1310", "line": 629, "column": 11, "nodeType": "1058", "messageId": "1059", "endLine": 629, "endColumn": 19}, {"ruleId": "1056", "severity": 1, "message": "1065", "line": 7, "column": 32, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 42}, {"ruleId": "1071", "severity": 1, "message": "1311", "line": 71, "column": 6, "nodeType": "1073", "endLine": 71, "endColumn": 45, "suggestions": "1312"}, {"ruleId": "1071", "severity": 1, "message": "1313", "line": 127, "column": 6, "nodeType": "1073", "endLine": 127, "endColumn": 8, "suggestions": "1314"}, {"ruleId": "1056", "severity": 1, "message": "1315", "line": 13, "column": 10, "nodeType": "1058", "messageId": "1059", "endLine": 13, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1316", "line": 70, "column": 6, "nodeType": "1073", "endLine": 70, "endColumn": 24, "suggestions": "1317"}, {"ruleId": "1056", "severity": 1, "message": "1140", "line": 7, "column": 8, "nodeType": "1058", "messageId": "1059", "endLine": 7, "endColumn": 14}, {"ruleId": "1071", "severity": 1, "message": "1318", "line": 61, "column": 6, "nodeType": "1073", "endLine": 61, "endColumn": 22, "suggestions": "1319"}, "no-unused-vars", "'Routes' is defined but never used.", "Identifier", "unusedVar", "'Route' is defined but never used.", "'Navigate' is defined but never used.", "'lazy' is defined but never used.", "'Link' is defined but never used.", "'Footer' is defined but never used.", "'sendOTPApi' is defined but never used.", "'organization_id' is assigned a value but never used.", "'isFormValid' is assigned a value but never used.", "'setIsFormValid' is assigned a value but never used.", "'setClassroomData' is assigned a value but never used.", "'dashboardData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["1320"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'decodeData' is defined but never used.", "'totalPages' is assigned a value but never used.", "'totalRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getAllClassroomData'. Either include it or remove the dependency array.", ["1321"], "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1322"], "'handleUpdateAssignment' is assigned a value but never used.", "'dueStatus' is assigned a value but never used.", "'formatDateForInput' is assigned a value but never used.", "'formatToLocalString' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrainees'. Either include it or remove the dependency array.", ["1323"], "React Hook useEffect has a missing dependency: 'fetchAvailableTrainees'. Either include it or remove the dependency array.", ["1324"], "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseList'. Either include it or remove the dependency array.", ["1325"], "React Hook useEffect has a missing dependency: 'fetchLiveClasses'. Either include it or remove the dependency array.", ["1326"], "'response' is assigned a value but never used.", "'formatDateTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseStats'. Either include it or remove the dependency array.", ["1327"], "'Icon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentDetails'. Either include it or remove the dependency array.", ["1328"], "'useCallback' is defined but never used.", "'location' is assigned a value but never used.", "'correctAnswers' is assigned a value but never used.", "'handleOptionContentTypeChange' is assigned a value but never used.", "'renderQuestionContent' is assigned a value but never used.", "'renderOptionContent' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getDocumentDetailsById'. Either include it or remove the dependency array.", ["1329"], "'handleDocumentRemove' is assigned a value but never used.", "'Loader' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseModule'. Either include it or remove the dependency array.", ["1330"], ["1331"], "'decodedCourseId' is assigned a value but never used.", "'REACT_APP_BITMOVIN_PLAYER_KEY' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleContent'. Either include it or remove the dependency array.", ["1332"], "React Hook useEffect has a missing dependency: 'fetchSurveyDetails'. Either include it or remove the dependency array.", ["1333"], "'encodeData' is defined but never used.", "'newVideo' is assigned a value but never used.", "'getQuestionBankById' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1334"], "'setPage' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "'documentPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCertificates'. Either include it or remove the dependency array.", ["1335"], "'handleCreateModalClose' is assigned a value but never used.", "'togglePreview' is assigned a value but never used.", "'sendAnnouncements' is defined but never used.", "'NoData' is defined but never used.", ["1336"], "'formatDate' is assigned a value but never used.", "'totalCount' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'notifications.length'. Either include it or remove the dependency array.", ["1337"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1338"], "'showDeleteModal' is assigned a value but never used.", "'setShowDeleteModal' is assigned a value but never used.", "'handleBulkUpload' is assigned a value but never used.", "'handleSampleDownload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTraineesData'. Either include it or remove the dependency array.", ["1339"], "'tabData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1340"], "'TodoList' is defined but never used.", "'monthlyPerformance' is assigned a value but never used.", "'setMonthlyPerformance' is assigned a value but never used.", "'changePassword' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchResults'. Either include it or remove the dependency array.", ["1341"], "'courseProgress' is assigned a value but never used.", "'setCourseProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleList'. Either include it or remove the dependency array.", ["1342"], "React Hook useEffect has a missing dependency: 'selectedContent'. Either include it or remove the dependency array.", ["1343"], "'handleCourseProgressUpdate' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaShareAlt' is defined but never used.", "'FaTwitter' is defined but never used.", "'FaLinkedin' is defined but never used.", "'FaLock' is defined but never used.", "React Hook useEffect has a missing dependency: 'getCourseDetails'. Either include it or remove the dependency array.", ["1344"], "'courseModules' is assigned a value but never used.", "'handleWatchCourse' is assigned a value but never used.", "'hasSubCategories' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'saveRecentVideo' is defined but never used.", "'videoId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initPlayer'. Either include it or remove the dependency array.", ["1345"], "'useEffect' is defined but never used.", "'AccountInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendtovalidation'. Either include it or remove the dependency array.", ["1346"], "'hasMoreQuestions' is assigned a value but never used.", "'availableCurrentPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentQuestions'. Either include it or remove the dependency array.", ["1347"], "'allResults' is assigned a value but never used.", "'groupedResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAnalyticsData' and 'fetchResults'. Either include them or remove the dependency array.", ["1348"], "'gradeClassroomAssignmentSubmission' is defined but never used.", "'assignmentDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentData'. Either include it or remove the dependency array.", ["1349"], "React Hook useEffect has a missing dependency: 'fetchSurveyData'. Either include it or remove the dependency array.", ["1350"], "React Hook useEffect has a missing dependency: 'fetchTraineeProgress'. Either include it or remove the dependency array.", ["1351"], "'classroomName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classroom_id'. Either include it or remove the dependency array.", ["1352"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has missing dependencies: 'fetchClassrooms' and 'search'. Either include them or remove the dependency array.", ["1353"], "'selectedFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleSubmit' and 'timeLeft'. Either include them or remove the dependency array.", ["1354"], ["1355"], "'editOrganisationUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'getTicketData'. Either include it or remove the dependency array.", ["1356"], ["1357"], ["1358"], "React Hook useEffect has a missing dependency: 'currentUserId'. Either include it or remove the dependency array.", ["1359"], "'toggleEmojiPicker' is assigned a value but never used.", "'dislikeMessage' is assigned a value but never used.", "'likesArray' is assigned a value but never used.", "'dislikesArray' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", ["1360"], ["1361"], "'moduleIndex' is assigned a value but never used.", "'collapsed' is assigned a value but never used.", "'setCollapsed' is assigned a value but never used.", ["1362"], ["1363"], "'classroomId' is assigned a value but never used.", "'toast' is defined but never used.", "'validateDate' is defined but never used.", "'profileData' is assigned a value but never used.", "'setProfileData' is assigned a value but never used.", "'handleCountrySelect' is assigned a value but never used.", "'handleImageChange' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClassroomData'. Either include it or remove the dependency array.", ["1364"], "React Hook useEffect has a missing dependency: 'fetchCourseData'. Either include it or remove the dependency array.", ["1365"], "React Hook useEffect has a missing dependency: 'fetchPaymentData'. Either include it or remove the dependency array.", ["1366"], "React Hook useEffect has a missing dependency: 'fetchTicketsData'. Either include it or remove the dependency array.", ["1367"], "React Hook useEffect has a missing dependency: 'fetchCertificatesData'. Either include it or remove the dependency array.", ["1368"], "'updateLike' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1369"], "'fetchReplies' is assigned a value but never used.", "'handleDeleteReply' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMonthlyPerformance'. Either include it or remove the dependency array.", ["1370"], "React Hook useEffect has a missing dependency: 'searchTerm'. Either include it or remove the dependency array.", ["1371"], ["1372"], "React Hook useEffect has a missing dependency: 'getAssessmentQuestions'. Either include it or remove the dependency array.", ["1373"], "'getScorePercentage' is assigned a value but never used.", "'fetchCourseProgress' is defined but never used.", ["1374"], ["1375"], "React Hook useEffect has a missing dependency: 'contentData'. Either include it or remove the dependency array.", ["1376"], "'saveVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'contentData?.title'. Either include it or remove the dependency array.", ["1377"], ["1378"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'totalQuestions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSurveyQuestions'. Either include it or remove the dependency array.", ["1379"], "'filteredData' is assigned a value but never used.", "'getStatusClass' is assigned a value but never used.", ["1380"], "'clearFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchResources'. Either include it or remove the dependency array.", ["1381"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1382"], "'updateComment' is defined but never used.", "React Hook useEffect has missing dependencies: 'getNotesData' and 'videoId'. Either include them or remove the dependency array.", ["1383"], ["1384"], "'actionType' is assigned a value but never used.", "'setSearchCountry' is assigned a value but never used.", "'filteredCountries' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1385"], "'handleStatusFilterChange' is assigned a value but never used.", "'newState' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["1386"], "'getTaskBadge' is assigned a value but never used.", ["1387"], "'resource_name' is assigned a value but never used.", "'fileName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeZoomMeeting'. Either include it or remove the dependency array.", ["1388"], "React Hook useEffect has missing dependencies: 'courseId', 'sendToValidation', and 'txnId'. Either include them or remove the dependency array.", ["1389"], "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleCancelValidation'. Either include it or remove the dependency array.", ["1390"], "React Hook useEffect has missing dependencies: 'courseData?.banner_image', 'courseData?.course_desc', 'courseData?.course_id', 'courseData?.course_name', 'courseData?.course_price', 'courseData?.course_type', 'courseData?.currency', 'courseData?.discountCode', 'courseData?.discountValue', and 'courseData?.points'. Either include them or remove the dependency array.", ["1391"], {"desc": "1392", "fix": "1393"}, {"desc": "1394", "fix": "1395"}, {"desc": "1396", "fix": "1397"}, {"desc": "1398", "fix": "1399"}, {"desc": "1400", "fix": "1401"}, {"desc": "1402", "fix": "1403"}, {"desc": "1404", "fix": "1405"}, {"desc": "1406", "fix": "1407"}, {"desc": "1408", "fix": "1409"}, {"desc": "1410", "fix": "1411"}, {"desc": "1412", "fix": "1413"}, {"desc": "1414", "fix": "1415"}, {"desc": "1416", "fix": "1417"}, {"desc": "1418", "fix": "1419"}, {"desc": "1420", "fix": "1421"}, {"desc": "1422", "fix": "1423"}, {"desc": "1392", "fix": "1424"}, {"desc": "1425", "fix": "1426"}, {"desc": "1427", "fix": "1428"}, {"desc": "1429", "fix": "1430"}, {"desc": "1431", "fix": "1432"}, {"desc": "1433", "fix": "1434"}, {"desc": "1435", "fix": "1436"}, {"desc": "1437", "fix": "1438"}, {"desc": "1439", "fix": "1440"}, {"desc": "1441", "fix": "1442"}, {"desc": "1443", "fix": "1444"}, {"desc": "1445", "fix": "1446"}, {"desc": "1447", "fix": "1448"}, {"desc": "1449", "fix": "1450"}, {"desc": "1451", "fix": "1452"}, {"desc": "1453", "fix": "1454"}, {"desc": "1455", "fix": "1456"}, {"desc": "1457", "fix": "1458"}, {"desc": "1459", "fix": "1460"}, {"desc": "1461", "fix": "1462"}, {"desc": "1463", "fix": "1464"}, {"desc": "1425", "fix": "1465"}, {"desc": "1427", "fix": "1466"}, {"desc": "1467", "fix": "1468"}, {"desc": "1469", "fix": "1470"}, {"desc": "1469", "fix": "1471"}, {"desc": "1469", "fix": "1472"}, {"desc": "1469", "fix": "1473"}, {"desc": "1474", "fix": "1475"}, {"desc": "1476", "fix": "1477"}, {"desc": "1478", "fix": "1479"}, {"desc": "1480", "fix": "1481"}, {"desc": "1482", "fix": "1483"}, {"desc": "1484", "fix": "1485"}, {"desc": "1486", "fix": "1487"}, {"desc": "1488", "fix": "1489"}, {"desc": "1490", "fix": "1491"}, {"desc": "1492", "fix": "1493"}, {"desc": "1488", "fix": "1494"}, {"desc": "1495", "fix": "1496"}, {"desc": "1497", "fix": "1498"}, {"desc": "1499", "fix": "1500"}, {"desc": "1441", "fix": "1501"}, {"desc": "1502", "fix": "1503"}, {"desc": "1504", "fix": "1505"}, {"desc": "1506", "fix": "1507"}, {"desc": "1508", "fix": "1509"}, {"desc": "1510", "fix": "1511"}, {"desc": "1484", "fix": "1512"}, {"desc": "1513", "fix": "1514"}, {"desc": "1515", "fix": "1516"}, {"desc": "1517", "fix": "1518"}, {"desc": "1519", "fix": "1520"}, {"desc": "1521", "fix": "1522"}, {"desc": "1523", "fix": "1524"}, {"desc": "1525", "fix": "1526"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1527", "text": "1528"}, "Update the dependencies array to be: [currentPage, getAllClassroomData, itemsPerPage, searchQuery]", {"range": "1529", "text": "1530"}, "Update the dependencies array to be: [decodedClassroomId, fetchAssignments]", {"range": "1531", "text": "1532"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", {"range": "1533", "text": "1534"}, "Update the dependencies array to be: [fetchAvailableTrainees, modalSearchTerm, showAddModal]", {"range": "1535", "text": "1536"}, "Update the dependencies array to be: [fetchCourseList]", {"range": "1537", "text": "1538"}, "Update the dependencies array to be: [classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1539", "text": "1540"}, "Update the dependencies array to be: [decodedCourseId, fetchCourseStats]", {"range": "1541", "text": "1542"}, "Update the dependencies array to be: [decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", {"range": "1543", "text": "1544"}, "Update the dependencies array to be: [decodedContentId, getDocumentDetailsById]", {"range": "1545", "text": "1546"}, "Update the dependencies array to be: [fetchCourseModule]", {"range": "1547", "text": "1548"}, "Update the dependencies array to be: [fetchCourseModule, search]", {"range": "1549", "text": "1550"}, "Update the dependencies array to be: [fetchModuleContent]", {"range": "1551", "text": "1552"}, "Update the dependencies array to be: [decodedSurveyId, decodedCourseId, fetchSurveyDetails]", {"range": "1553", "text": "1554"}, "Update the dependencies array to be: [fetchQuestions, itemsPerPage]", {"range": "1555", "text": "1556"}, "Update the dependencies array to be: [page, limit, fetchCertificates]", {"range": "1557", "text": "1558"}, {"range": "1559", "text": "1528"}, "Update the dependencies array to be: [debouncedSearchTerm, notifications.length]", {"range": "1560", "text": "1561"}, "Update the dependencies array to be: [debouncedSearchTerm, fetchNotifications]", {"range": "1562", "text": "1563"}, "Update the dependencies array to be: [pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", {"range": "1564", "text": "1565"}, "Update the dependencies array to be: [decodedId, fetchAnalyticsData]", {"range": "1566", "text": "1567"}, "Update the dependencies array to be: [assessmentId, fetchResults]", {"range": "1568", "text": "1569"}, "Update the dependencies array to be: [courseId, fetchModuleList]", {"range": "1570", "text": "1571"}, "Update the dependencies array to be: [moduleList, selectedContent]", {"range": "1572", "text": "1573"}, "Update the dependencies array to be: [courseId, getCourseDetails]", {"range": "1574", "text": "1575"}, "Update the dependencies array to be: [initPlayer]", {"range": "1576", "text": "1577"}, "Update the dependencies array to be: [course_id, sendtovalidation, session_id]", {"range": "1578", "text": "1579"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", {"range": "1580", "text": "1581"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", {"range": "1582", "text": "1583"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchAssessmentData]", {"range": "1584", "text": "1585"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchSurveyData]", {"range": "1586", "text": "1587"}, "Update the dependencies array to be: [decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", {"range": "1588", "text": "1589"}, "Update the dependencies array to be: [classroom_id, encodedClassroomID, urlActiveTab]", {"range": "1590", "text": "1591"}, "Update the dependencies array to be: [fetchClassrooms, page, search]", {"range": "1592", "text": "1593"}, "Update the dependencies array to be: [handleSubmit, quizStarted, submissionResult, timeLeft]", {"range": "1594", "text": "1595"}, "Update the dependencies array to be: [assignment_id, classroom_id, fetchQuestions, user_id]", {"range": "1596", "text": "1597"}, "Update the dependencies array to be: [page, limit, searchTerm, getTicketData]", {"range": "1598", "text": "1599"}, {"range": "1600", "text": "1561"}, {"range": "1601", "text": "1563"}, "Update the dependencies array to be: [classroom_id, currentUserId, group_id, group_name, token]", {"range": "1602", "text": "1603"}, "Update the dependencies array to be: [fetchNotifications]", {"range": "1604", "text": "1605"}, {"range": "1606", "text": "1605"}, {"range": "1607", "text": "1605"}, {"range": "1608", "text": "1605"}, "Update the dependencies array to be: [fetchClassroomData, traineeId]", {"range": "1609", "text": "1610"}, "Update the dependencies array to be: [fetchCourseData, traineeId]", {"range": "1611", "text": "1612"}, "Update the dependencies array to be: [fetchPaymentData, traineeId]", {"range": "1613", "text": "1614"}, "Update the dependencies array to be: [fetchTicketsData, traineeId]", {"range": "1615", "text": "1616"}, "Update the dependencies array to be: [fetchCertificatesData, traineeId]", {"range": "1617", "text": "1618"}, "Update the dependencies array to be: [fetchComments, videoId]", {"range": "1619", "text": "1620"}, "Update the dependencies array to be: [fetchMonthlyPerformance]", {"range": "1621", "text": "1622"}, "Update the dependencies array to be: [searchTerm]", {"range": "1623", "text": "1624"}, "Update the dependencies array to be: [page, searchTerm]", {"range": "1625", "text": "1626"}, "Update the dependencies array to be: [getAssessmentQuestions, moduleData.id]", {"range": "1627", "text": "1628"}, {"range": "1629", "text": "1624"}, "Update the dependencies array to be: [page, hasMore, isLoading, searchTerm]", {"range": "1630", "text": "1631"}, "Update the dependencies array to be: [contentData, contentData?.id]", {"range": "1632", "text": "1633"}, "Update the dependencies array to be: [videoUrl, contentData?.id, contentData?.title]", {"range": "1634", "text": "1635"}, {"range": "1636", "text": "1577"}, "Update the dependencies array to be: [surveyId, moduleData?.completed, fetchSurveyQuestions]", {"range": "1637", "text": "1638"}, "Update the dependencies array to be: [classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1639", "text": "1640"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", {"range": "1641", "text": "1642"}, "Update the dependencies array to be: [videoId, courseId, fetchReviews]", {"range": "1643", "text": "1644"}, "Update the dependencies array to be: [getNotesData, moduleId, videoId]", {"range": "1645", "text": "1646"}, {"range": "1647", "text": "1620"}, "Update the dependencies array to be: [currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", {"range": "1648", "text": "1649"}, "Update the dependencies array to be: [fetchTasks]", {"range": "1650", "text": "1651"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, selectedResourceType, fetchResources]", {"range": "1652", "text": "1653"}, "Update the dependencies array to be: [initializationStarted, loading, error, initializeZoomMeeting]", {"range": "1654", "text": "1655"}, "Update the dependencies array to be: [courseId, sendToValidation, txnId]", {"range": "1656", "text": "1657"}, "Update the dependencies array to be: [course_id, handleCancelValidation, txnid]", {"range": "1658", "text": "1659"}, "Update the dependencies array to be: [courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", {"range": "1660", "text": "1661"}, [8379, 8381], "[fetchDashboardData]", [4416, 4456], "[currentPage, getAllClassroomData, itemsPerPage, searchQuery]", [3270, 3290], "[decodedClassroomId, fetchAssignments]", [5315, 5374], "[decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", [5697, 5728], "[fetchAvailableTrainees, modalSearchTerm, showAddModal]", [2474, 2476], "[fetchCourseList]", [2868, 2944], "[classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [935, 952], "[decodedCourseId, fetchCourseStats]", [810, 848], "[decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", [5972, 5990], "[decodedContentId, getDocumentDetailsById]", [3092, 3094], "[fetchCourseModule]", [3269, 3277], "[fetchCourseModule, search]", [4264, 4266], "[fetchModuleContent]", [757, 791], "[decodedSurveyId, decodedCourseId, fetchSurveyDetails]", [10267, 10281], "[fetchQuestions, itemsPerPage]", [2486, 2499], "[page, limit, fetchCertificates]", [3277, 3279], [3318, 3339], "[debouncedSearchTerm, notifications.length]", [3597, 3618], "[debouncedSearchTerm, fetchNotifications]", [17596, 17666], "[pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", [2624, 2635], "[decodedId, fetchAnalyticsData]", [592, 606], "[assessmentId, fetchResults]", [1218, 1228], "[courseId, fetchModuleList]", [1678, 1690], "[module<PERSON>ist, <PERSON><PERSON><PERSON><PERSON>]", [2615, 2625], "[courseId, getCourseDetails]", [1876, 1878], "[initPlayer]", [1573, 1596], "[course_id, sendtovalidation, session_id]", [2503, 2558], "[decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", [2091, 2171], "[decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", [831, 873], "[decodedCourseId, search, pagination.page, fetchAssessmentData]", [806, 848], "[decodedCourseId, search, pagination.page, fetchSurveyData]", [920, 976], "[decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", [3577, 3611], "[classroom_id, encodedClassroomID, urlActiveTab]", [2869, 2875], "[fetchClassrooms, page, search]", [4500, 4531], "[handleSubmit, quizStarted, submissionResult, timeLeft]", [2728, 2766], "[assignment_id, classroom_id, fetchQuestions, user_id]", [1330, 1355], "[page, limit, searchTerm, getTicketData]", [3318, 3339], [3597, 3618], [8665, 8708], "[classroom_id, currentUserId, group_id, group_name, token]", [1557, 1559], "[fetchNotifications]", [1815, 1817], [1440, 1442], [1698, 1700], [1209, 1220], "[fetchClassroom<PERSON><PERSON>, traineeId]", [1666, 1677], "[fetchCourseData, traineeId]", [1330, 1341], "[fetchPayment<PERSON><PERSON>, traineeId]", [1325, 1336], "[fetchTickets<PERSON>ata, traineeId]", [1229, 1240], "[fetchCertificates<PERSON><PERSON>, traineeId]", [1367, 1376], "[fetchComments, videoId]", [393, 395], "[fetchMonthlyPerformance]", [3059, 3061], "[searchTerm]", [3153, 3159], "[page, searchTerm]", [1278, 1293], "[getAssessmentQuestions, moduleData.id]", [816, 818], [1229, 1255], "[page, hasMore, isLoading, searchTerm]", [1761, 1778], "[contentData, contentData?.id]", [6036, 6063], "[videoUrl, contentData?.id, contentData?.title]", [6796, 6798], [1804, 1837], "[surveyId, moduleData?.completed, fetchSurveyQuestions]", [1525, 1602], "[classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [2287, 2354], "[decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", [1270, 1289], "[videoId, courseId, fetchReviews]", [673, 683], "[getNotesData, moduleId, videoId]", [1475, 1484], [4256, 4310], "[currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", [824, 826], "[fetchTasks]", [1904, 1959], "[decodedClassroomId, currentPage, selectedResourceType, fetchResources]", [2406, 2445], "[initializationStarted, loading, error, initializeZoomMeeting]", [3808, 3810], "[courseId, sendToValidation, txnId]", [2264, 2282], "[course_id, handleCancelValidation, txnid]", [1850, 1866], "[courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]"]