import React, { useState, useEffect, useCallback } from 'react';
import { Icon } from '@iconify/react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import './CreateCourse.css';
import { decodeData } from '../../../utils/encodeAndEncode';
import { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';
import courseCategoriesData from '../../../utils/courseCategories.json';
const domain = process.env.REACT_APP_DOMAIN_URL?.replace(/"/g, '').trim();

function CreateCourse() {
  const location = useLocation();
  const navigate = useNavigate();
  const { courseId } = useParams();
  // Only try to decode if courseId exists
  const decodedCourseId = courseId ? decodeData(courseId) : null;
  // console.log('Course ID:', courseId);
  // console.log('Decoded Course ID:', decodedCourseId);

  const isEditing = location.state?.isEditing;
  const initialCourseData = location.state?.courseData;

  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingCourse, setIsLoadingCourse] = useState(false);
  const [formData, setFormData] = useState({
    courseTitle: '',
    courseDescription: '',
    courseLevel: 'intermediate',
    courseLanguage: 'english',
    courseCategory: '',
    thumbnail: null,
    thumbnailPreview: '',
    certificateType: '',
    courseType: 'free',
    currency: domain === 'lms.tpi.sg' ? 'SGD' : 
              (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? 'INR' : 'USD',
    price: '',
    tags: [],
    courseInfo: []
  });

  useEffect(() => {
    if (isEditing && initialCourseData) {
      setFormData(initialCourseData);
    }
  }, [isEditing, initialCourseData]);

  // Fetch course details if courseId is available
  useEffect(() => {
    const fetchCourseDetails = async () => {
      if (courseId && decodedCourseId) {
        setIsLoadingCourse(true);
        try {
          console.log('Fetching course details for ID:', decodedCourseId);
          const response = await getCourseDetailsById({ course_id: decodedCourseId });

          console.log('get Course Details Response:', response);

          if (response.success && response.data.course) {
            const course = response.data.course;
            console.log('Course details fetched:', course);

            // Map API response to form data structure
            setFormData({
              courseTitle: course.course_name || '',
              courseDescription: course.course_desc || '',
              courseLevel: course.levels || '',
              courseLanguage: course.course_language || '',
              courseCategory: course.course_category || '',
              thumbnail: null, // Will be handled separately if needed
              thumbnailPreview: course.banner_image || '',
              certificateType: course.certificate_template_id?.toString() || '',
              courseType: course.course_type || 'free',
              currency: course.currency || 'USD',
              price: course.course_price || '',
              tags: Array.isArray(course.tags) ? course.tags : [],
              courseInfo: Array.isArray(course.course_info) ? course.course_info : []
            });

            console.log('Form data populated from API');
          } else {
            console.error('Failed to fetch course details:', response.data?.error_msg);
            alert('Failed to load course details: ' + (response.data?.error_msg || 'Unknown error'));
          }
        } catch (error) {
          console.error('Error fetching course details:', error);
          alert('Error loading course details. Please try again.');
        } finally {
          setIsLoadingCourse(false);
        }
      }
    };

    fetchCourseDetails();
  }, [courseId, decodedCourseId]);

  const [certificateData, setCertificateData] = useState([]);

  const fetchCertificateData = async () => {
    try {
      const response = await getCourseCertificate();
      console.log('Certificate Data:', response);
      if (response.success) {
        setCertificateData(response.data.templates);
      } else {
        // console.error('Invalid certificate data format:', response);
        setCertificateData([]); // Reset to empty array if invalid data
      }
    } catch (error) {
      console.error('Error fetching certificate data:', error);
      setCertificateData([]); // Reset to empty array on error
    }
  };

  useEffect(() => {
    fetchCertificateData();
  }, []);

  const [tagInput, setTagInput] = useState('');
  const [infoInput, setInfoInput] = useState('');
  const [editingInfo, setEditingInfo] = useState(null);

  // Memoized handlers for tag and info inputs
  const handleTagInputChange = useCallback((e) => {
    setTagInput(e.target.value);
  }, []);

  const handleInfoInputChange = useCallback((e) => {
    setInfoInput(e.target.value);
  }, []);

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  }, []);

  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prevState => ({
        ...prevState,
        thumbnail: file,
        thumbnailPreview: URL.createObjectURL(file)
      }));
    }
  };

  const handleRemoveFile = (type) => {
    if (type === 'thumbnail') {
      setFormData(prevState => ({
        ...prevState,
        thumbnail: null,
        thumbnailPreview: ''
      }));
    }
  };

  const handleAddTag = useCallback((e) => {
    e.preventDefault();
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      if (tag.length < 3) {
        alert('Tag must be at least 3 characters long');
        return;
      }
      if (formData.tags.length >= 8) {
        alert('Maximum 8 tags allowed');
        return;
      }
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  }, [tagInput, formData.tags]);

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleTagInputKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag(e);
    }
  };

  const handleAddInfo = useCallback((e) => {
    e.preventDefault();
    if (infoInput.trim()) {
      if (editingInfo !== null) {
        setFormData(prev => ({
          ...prev,
          courseInfo: prev.courseInfo.map((info, index) =>
            index === editingInfo ? infoInput.trim() : info
          )
        }));
        setEditingInfo(null);
      } else {
        setFormData(prev => ({
          ...prev,
          courseInfo: [...prev.courseInfo, infoInput.trim()]
        }));
      }
      setInfoInput('');
    }
  }, [infoInput, editingInfo]);

  const handleEditInfo = (index) => {
    setInfoInput(formData.courseInfo[index]);
    setEditingInfo(index);
  };

  const handleDeleteInfo = (index) => {
    setFormData(prev => ({
      ...prev,
      courseInfo: prev.courseInfo.filter((_, i) => i !== index)
    }));
  };

  const handleInfoInputKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddInfo(e);
    }
  };

  const steps = [
    { number: '01', title: 'Course Information' },
    { number: '02', title: 'Course Media' },
    { number: '03', title: 'Additional information' },
    { number: '04', title: 'Pricing' },
    { number: '05', title: 'Preview & Submit' }
  ];

  // Validation functions for each step
  const validateStep1 = () => {
    return formData.courseTitle.trim() !== '' &&
           formData.courseDescription.trim() !== '' &&
           formData.courseLevel !== '' &&
           formData.courseLanguage !== '' &&
           formData.courseCategory !== '';
  };

  const validateStep2 = () => {
    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)
    if (courseId && decodedCourseId) {
      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') &&
             formData.certificateType !== '';
    }
    // For new course creation, require new thumbnail upload
    return formData.thumbnail !== null && formData.certificateType !== '';
  };

  const validateStep3 = () => {
    // For editing mode, allow empty tags and courseInfo if course already exists
    if (courseId && decodedCourseId) {
      return true; // Allow progression in edit mode even with empty arrays
    }
    // For new course creation, require at least one tag and one course info
    return formData.tags.length > 0 && formData.courseInfo.length > 0;
  };

  const validateStep4 = () => {
    if (formData.courseType === 'free') {
      return true;
    }
    return formData.price !== '' && parseFloat(formData.price) > 0;
  };

  const validateStep5 = () => {
    // Preview step - all previous steps must be valid
    return validateStep1() && validateStep2() && validateStep3() && validateStep4();
  };

  const isCurrentStepValid = () => {
    switch (currentStep) {
      case 1:
        return validateStep1();
      case 2:
        return validateStep2();
      case 3:
        return validateStep3();
      case 4:
        return validateStep4();
      case 5:
        return validateStep5();
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (isCurrentStepValid()) {
      setIsSubmitting(true);

      try {
        // Start the minimum 3-second timer
        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));

        // Create FormData object for file upload
        const submitData = new FormData();

        // Add all form fields to FormData
        submitData.append('courseTitle', formData.courseTitle);
        submitData.append('courseDescription', formData.courseDescription);
        submitData.append('courseLevel', formData.courseLevel);
        submitData.append('courseLanguage', formData.courseLanguage);
        submitData.append('courseCategory', formData.courseCategory);
        submitData.append('certificateType', formData.certificateType);
        submitData.append('courseType', formData.courseType);
        submitData.append('currency', formData.currency);
        submitData.append('price', formData.price);
        submitData.append('tags', JSON.stringify(formData.tags));
        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));

        // add loop print the data 
        for (const [key, value] of Object.entries(formData)) {
          console.log(`${key}: ${value}`);
        }

        // Add thumbnail file if exists (for new upload)
        if (formData.thumbnail) {
          submitData.append('banner_image', formData.thumbnail);
        }

        console.log('Submitting course data...');
        console.log('Is editing mode:', courseId && decodedCourseId);

        // Choose API call based on edit mode
        let apiCall;
        if (courseId && decodedCourseId) {
          // Update existing course
          apiCall = updateCourseDetails(decodedCourseId, submitData);
        } else {
          // Create new course
          apiCall = createNewCourse(submitData);
        }

        // Wait for both minimum time and API response
        const [response] = await Promise.all([apiCall, minLoadingTime]);

        if (response.success) {
          console.log('Course operation successful:', response.data);
          // Navigate to courses page after successful operation
          navigate('/admin/courses');
        } else {
          setIsSubmitting(false);
          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';
          alert(errorMsg + ': ' + (response.data?.error_msg || 'Unknown error'));
        }
      } catch (error) {
        setIsSubmitting(false);
        console.error('Error with course operation:', error);
        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';
        alert(errorMsg + '. Please try again.');
      }
    } else {
      alert('Please fill in all required fields before submitting.');
    }
  };

  const renderCourseInformationForm = () => (
    <div className="course-information">
      <div className="row">
        <div className="col-12 mb-3">
          <label htmlFor="courseTitle" className="form-label">Course Title *</label>
          <input
            type="text"
            className="form-control"
            id="courseTitle"
            name="courseTitle"
            placeholder="Enter course title"
            value={formData.courseTitle}
            onChange={handleInputChange}
            maxLength={100}
            required
          />
          <small className="text-muted">Maximum 100 characters allowed. Use a short and meaningful title for better readability.</small>
        </div>

        <div className="col-12 mb-3">
          <label htmlFor="courseDescription" className="form-label">Course Description *</label>
          <textarea
            className="form-control"
            id="courseDescription"
            name="courseDescription"
            rows="4"
            placeholder="Enter course description"
            value={formData.courseDescription}
            onChange={handleInputChange}
            maxLength={1000}
            required
          ></textarea>
          <small className="text-muted">Maximum 1000 characters allowed</small>
        </div>

        <div className="col-md-4 mb-3">
          <label htmlFor="courseLevel" className="form-label">Course Level *</label>
          <select
            className="form-select"
            id="courseLevel"
            name="courseLevel"
            value={formData.courseLevel}
            onChange={handleInputChange}
            required
          >
            <option value="">Select Level</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
            <option value="all-levels">All Levels</option>
          </select>
        </div>

        <div className="col-md-4 mb-3">
          <label htmlFor="courseLanguage" className="form-label">Course Language *</label>
          <select
            className="form-select"
            id="courseLanguage"
            name="courseLanguage"
            value={formData.courseLanguage}
            onChange={handleInputChange}
            required
          >
            <option value="">Select Language</option>
            <option value="english">English</option>
            <option value="spanish">Spanish</option>
            <option value="french">French</option>
            <option value="german">German</option>
            <option value="chinese">Chinese</option>
            <option value="japanese">Japanese</option>
            <option value="hindi">Hindi</option>
          </select>
        </div>

        <div className="col-md-4 mb-3">
          <label htmlFor="courseCategory" className="form-label">Course Category *</label>
          <select
            className="form-select"
            id="courseCategory"
            name="courseCategory"
            value={formData.courseCategory}
            onChange={handleInputChange}
            required
          >
            <option value="">Select Category</option>
            {courseCategoriesData.categories.map((category, index) => (
              <option key={index} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );

  const renderCourseMediaForm = () => (
    <div className="course-media">
      <div className="row">
        {/* Thumbnail Upload */}
        <div className="col-md-6 mb-4">
          <label className="form-label">Course Thumbnail *</label>
          <div className="card">
            <div className="card-body">
              {!formData.thumbnailPreview ? (
                <div className="text-center p-4">
                  <input
                    type="file"
                    id="thumbnail"
                    className="d-none"
                    accept="image/*"
                    onChange={handleThumbnailChange}
                  />
                  <label 
                    htmlFor="thumbnail" 
                    className="btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto"
                    style={{ width: 'fit-content', cursor: 'pointer' }}
                  >
                    <Icon icon="fluent:image-add-24-regular" width="48" height="48" />
                    <span>Upload Thumbnail</span>
                    <small className="text-muted">Recommended size: 1280x720px</small>
                  </label>
                </div>
              ) : (
                <div className="position-relative">
                  <img
                    src={formData.thumbnailPreview}
                    alt="Course Thumbnail"
                    className="img-fluid rounded"
                    style={{ width: '100%', height: 'auto' }}
                  />
                  <button
                    className="btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute"
                    onClick={() => handleRemoveFile('thumbnail')}
                    type="button"
                    style={{ 
                      width: '24px', 
                      height: '24px', 
                      top: '8px', 
                      right: '8px',
                      minWidth: 'unset',
                      borderRadius: '4px'
                    }}
                  >
                    <Icon icon="fluent:delete-24-regular" width="14" height="14" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Certificate Selection */}
        <div className="col-md-6 mb-4">
          <label className="form-label">Certificate Type *</label>
          <div className="card">
            <div className="card-body">
              <select
                className="form-select mb-3"
                name="certificateType"
                value={formData.certificateType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Certificate Type</option>
                {Array.isArray(certificateData) && certificateData.map((cert) => (
                  <option key={cert.id || cert._id} value={cert.id || cert._id}>
                    {cert.template_name || 'Unnamed Certificate'}
                  </option>
                ))}
              </select>

              {formData.certificateType && (
                <div className="certificate-preview p-3 bg-light rounded">
                  <div className="d-flex align-items-center gap-3">
                    <Icon icon="fluent:certificate-24-regular" width="32" height="32" className="text-primary" />
                    <div>
                      <h6 className="mb-1">{formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1)} Certificate</h6>
                      <small className="text-muted">Will be issued upon course completion</small>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAdditionalInformationForm = () => (
    <div className="row">
      <div className="col-12 mb-4">
        <div className="card">
          <div className="card-body">
            <div className="mb-4">
              <label className="form-label">Course Tags *</label>
              <div className="mb-2">
                <div className="row g-2">
                  <div className="col-10">
                    <input
                      type="text"
                      className="form-control rounded-0 rounded-start"
                      placeholder="Add tags (e.g., 'programming', 'web development')"
                      value={tagInput}
                      onChange={handleTagInputChange}
                      onKeyDown={handleTagInputKeyDown}
                    />
                  </div>
                  <div className="col-2">
                    <button 
                      className="btn btn-primary w-100 rounded-0 rounded-end"
                      onClick={handleAddTag}
                    >
                      Add Tag
                    </button>
                  </div>
                </div>
                <div className="mt-2">
                  <small className="d-block text-muted mb-1">Press Enter or click Add Tag to add</small>
                  <small className="d-block text-muted">
                    • Minimum 3 characters per tag
                    <br />
                    • Maximum 8 tags allowed
                    <br />
                    • {8 - formData.tags.length} tags remaining
                  </small>
                </div>
              </div>
              <div className="d-flex flex-wrap gap-2 mt-3">
                {formData.tags.map((tag, index) => (
                  <div 
                    key={index}
                    className="badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3"
                  >
                    {tag}
                    <button
                      type="button"
                      className="btn btn-link text-danger p-0 d-flex align-items-center"
                      onClick={() => handleRemoveTag(tag)}
                      style={{ minWidth: '20px', height: '20px' }}
                    >
                      <Icon icon="fluent:dismiss-24-regular" width="16" height="16" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <label className="form-label">Course Info *</label>
              <div className="mb-2">
                <div className="row g-2">
                  <div className="col-10">
                    <input
                      type="text"
                      className="form-control rounded-0 rounded-start"
                      placeholder="Add course information point (e.g., 'Lifetime access to course materials')"
                      value={infoInput}
                      onChange={handleInfoInputChange}
                      onKeyDown={handleInfoInputKeyDown}
                    />
                  </div>
                  <div className="col-2">
                    <button
                      className="btn btn-primary w-100 rounded-0 rounded-end"
                      onClick={handleAddInfo}
                    >
                      {editingInfo !== null ? 'Update' : 'Add Info'}
                    </button>
                  </div>
                </div>
                <div className="mt-2">
                  <small className="d-block text-muted">
                    Add important information points about your course
                  </small>
                </div>
              </div>

              <div className="mt-3">
                {formData.courseInfo.map((info, index) => (
                  <div
                    key={index}
                    className="d-flex align-items-center gap-2 p-2 border rounded mb-2"
                  >
                    <Icon
                      icon="fluent:info-24-regular"
                      className="text-primary flex-shrink-0"
                      width="20"
                      height="20"
                    />
                    <span className="flex-grow-1">{info}</span>
                    <div className="d-flex gap-2">
                      <button
                        type="button"
                        className="btn btn-link text-primary p-0 d-flex align-items-center"
                        onClick={() => handleEditInfo(index)}
                        style={{ minWidth: '20px', height: '20px' }}
                      >
                        <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                      </button>
                      <button
                        type="button"
                        className="btn btn-link text-danger p-0 d-flex align-items-center"
                        onClick={() => handleDeleteInfo(index)}
                        style={{ minWidth: '20px', height: '20px' }}
                      >
                        <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreviewStep = () => (
    <div className="row">
      <div className="col-12">
        <div className="card">
          <div className="card-header">
            <h5 className="mb-0">
              <Icon icon="fluent:eye-24-regular" width="20" height="20" className="me-2" />
              Course Preview
            </h5>
            <p className="text-muted mb-0 mt-2">
              Review all your course information before submitting. If you need to make changes, use the "Previous" button to go back to any step.
            </p>
          </div>
          <div className="card-body">
            {/* Course Information Section */}
            <div className="mb-4">
              <h6 className="text-primary mb-3">
                <Icon icon="fluent:info-24-regular" width="18" height="18" className="me-2" />
                Course Information
              </h6>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label text-muted">Course Title</label>
                  <p className="mb-0 fw-medium">{formData.courseTitle || 'Not specified'}</p>
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label text-muted">Course Category</label>
                  <p className="mb-0">{formData.courseCategory || 'Not specified'}</p>
                </div>
                <div className="col-12 mb-3">
                  <label className="form-label text-muted">Course Description</label>
                  <p className="mb-0">{formData.courseDescription || 'Not specified'}</p>
                </div>
                <div className="col-md-4 mb-3">
                  <label className="form-label text-muted">Level</label>
                  <p className="mb-0 text-capitalize">{formData.courseLevel || 'Not specified'}</p>
                </div>
                <div className="col-md-4 mb-3">
                  <label className="form-label text-muted">Language</label>
                  <p className="mb-0 text-capitalize">{formData.courseLanguage || 'Not specified'}</p>
                </div>
                <div className="col-md-4 mb-3">
                  <label className="form-label text-muted">Certificate</label>
                  <p className="mb-0">
                    {formData.certificateType ? 
                      (() => {
                        const selectedCert = certificateData.find(cert => 
                          (cert.id || cert._id)?.toString() === formData.certificateType?.toString()
                        );
                        return selectedCert ? selectedCert.template_name : `Certificate ID: ${formData.certificateType}`;
                      })() 
                      : 'Not specified'
                    }
                  </p>
                </div>
              </div>
            </div>

            <hr />

            {/* Course Media Section */}
            <div className="mb-4">
              <h6 className="text-primary mb-3">
                <Icon icon="fluent:image-24-regular" width="18" height="18" className="me-2" />
                Course Media
              </h6>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label text-muted">Course Thumbnail</label>
                  {formData.thumbnailPreview ? (
                    <div>
                      <img
                        src={formData.thumbnailPreview}
                        alt="Course Thumbnail"
                        className="img-fluid rounded border"
                        style={{ maxHeight: '150px', width: 'auto' }}
                      />
                    </div>
                  ) : (
                    <p className="mb-0 text-muted">No thumbnail uploaded</p>
                  )}
                </div>
              </div>
            </div>

            <hr />

            {/* Additional Information Section */}
            <div className="mb-4">
              <h6 className="text-primary mb-3">
                <Icon icon="fluent:tag-24-regular" width="18" height="18" className="me-2" />
                Additional Information
              </h6>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label text-muted">Tags</label>
                  {formData.tags.length > 0 ? (
                    <div className="d-flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span key={index} className="badge bg-light text-dark border">
                          {tag}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="mb-0 text-muted">No tags added</p>
                  )}
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label text-muted">Course Information Points</label>
                  {formData.courseInfo.length > 0 ? (
                    <ul className="list-unstyled mb-0">
                      {formData.courseInfo.map((info, index) => (
                        <li key={index} className="mb-1">
                          <Icon icon="fluent:checkmark-circle-24-regular" width="16" height="16" className="text-success me-2" />
                          {info}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="mb-0 text-muted">No course information added</p>
                  )}
                </div>
              </div>
            </div>

            <hr />

            {/* Pricing Section */}
            <div className="mb-4">
              <h6 className="text-primary mb-3">
                <Icon icon="fluent:payment-24-regular" width="18" height="18" className="me-2" />
                Pricing Information
              </h6>
              <div className="row">
                <div className="col-md-3 mb-3">
                  <label className="form-label text-muted">Course Type</label>
                  <p className="mb-0">
                    <span className={`badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`}>
                      {formData.courseType === 'free' ? 'Free Course' : 'Paid Course'}
                    </span>
                  </p>
                </div>
                {formData.courseType === 'paid' && (
                  <>
                    <div className="col-md-3 mb-3">
                      <label className="form-label text-muted">Currency</label>
                      <p className="mb-0">{formData.currency}</p>
                    </div>
                    <div className="col-md-3 mb-3">
                      <label className="form-label text-muted">Price</label>
                      <p className="mb-0 fw-medium">
                        {formData.currency === 'USD' && '$'}
                        {formData.currency === 'EUR' && '€'}
                        {formData.currency === 'GBP' && '£'}
                        {formData.currency === 'INR' && '₹'}
                        {formData.currency === 'SGD' && 'S$'}
                        {formData.price}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPricingStep = () => (
    <div className="row">
      <div className="col-12 mb-4">
        <div className="card">
          <div className="card-body">
  
            <div className="mb-4">
              <label className="form-label">Course Type *</label>
              <div className="d-flex gap-4">
                <div className="form-check">
                  <input
                    type="radio"
                    className="form-check-input"
                    id="free"
                    name="courseType"
                    value="free"
                    checked={formData.courseType === 'free'}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      courseType: e.target.value,
                      price: ''
                      // Removed currency override to keep selected currency
                    }))}
                  />
                  <label className="form-check-label" htmlFor="free">
                    <strong>Free Course</strong>
                    <br />
                    <small className="text-muted">No payment required for enrollment</small>
                  </label>
                </div>
                <div className="form-check">
                  <input
                    type="radio"
                    className="form-check-input"
                    id="paid"
                    name="courseType"
                    value="paid"
                    checked={formData.courseType === 'paid'}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      courseType: e.target.value
                    }))}
                  />
                  <label className="form-check-label" htmlFor="paid">
                    <strong>Paid Course</strong>
                    <br />
                    <small className="text-muted">Students must purchase to access content</small>
                  </label>
                </div>
              </div>
            </div>



            {formData.courseType === 'paid' && (
  <>
    {/* SGD for TPI Domain */}
    {domain === 'lms.tpi.sg' && (
      <div className="mb-3">
        <label className="form-label">Select Currency</label>
        <select
          className="form-select"
          value={formData.currency}
          onChange={(e) => {
            console.log('Currency changed from:', formData.currency, 'to:', e.target.value);
            setFormData((prev) => ({
              ...prev,
              currency: e.target.value
            }));
          }}
        >
          <option value="SGD">SGD (S$)</option>
        </select>
        <small className="text-muted">
          Note: Only SGD is supported on this Platform.
        </small>
      </div>
    )}

    {/* INR for NxGen Varsity */}
    {domain === 'lms.nxgenvarsity.com' && (
      <div className="mb-3">
        <label className="form-label">Select Currency</label>
        <select
          className="form-select"
          value={formData.currency}
          onChange={(e) => {
            console.log('Currency changed from:', formData.currency, 'to:', e.target.value);
            setFormData((prev) => ({
              ...prev,
              currency: e.target.value
            }));
          }}
        >
          <option value="INR">INR (₹)</option>
        </select>
        <small className="text-muted">
          Note: Only INR is supported on this domain.
        </small>
      </div>
    )}

    {/* INR for Creator Foundation */}
    {domain === 'lms.creatorfoundation.in' && (
      <div className="mb-3">
        <label className="form-label">Select Currency</label>
        <select
          className="form-select"
          value={formData.currency}
          onChange={(e) => {
            console.log('Currency changed from:', formData.currency, 'to:', e.target.value);
            setFormData((prev) => ({
              ...prev,
              currency: e.target.value
            }));
          }}
        >
          <option value="INR">INR (₹)</option>
        </select>
        <small className="text-muted">
          Note: Only INR is supported on this domain.
        </small>
      </div>
    )}

    {/* Course Price Input */}
    <div className="mb-3">
      <label className="form-label">Course Price *</label>
      <div className="input-group">
        <span className="input-group-text">
          {formData.currency === 'SGD' && 'S$'}
          {formData.currency === 'INR' && '₹'}
        </span>
        <input
          type="number"
          className="form-control"
          placeholder="Enter course price"
          value={formData.price}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              price: e.target.value
            }))
          }
          required
        />
      </div>
    </div>
  </>
)}

          </div>
        </div>
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderCourseInformationForm();
      case 2:
        return renderCourseMediaForm();
      case 3:
        return renderAdditionalInformationForm();
      case 4:
        return renderPricingStep();
      case 5:
        return renderPreviewStep();
      default:
        return null;
    }
  };

  // Loading overlay component for submission
  const SubmissionLoadingOverlay = () => {
    const isEditMode = courseId && decodedCourseId;
    return (
      <div className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
           style={{
             backgroundColor: 'rgba(0, 0, 0, 0.7)',
             zIndex: 9999
           }}>
        <div className="text-center text-white">
          <div className="spinner-border text-primary mb-3" role="status" style={{ width: '3rem', height: '3rem' }}>
            <span className="visually-hidden">Loading...</span>
          </div>
          <h5 className="mb-2">{isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'}</h5>
          <p className="mb-0">Please wait while we process your course information.</p>
        </div>
      </div>
    );
  };

  // Loading overlay component for fetching course details
  const CourseLoadingOverlay = () => (
    <div className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
         style={{
           backgroundColor: 'rgba(0, 0, 0, 0.7)',
           zIndex: 9999
         }}>
      <div className="text-center text-white">
        <div className="spinner-border text-primary mb-3" role="status" style={{ width: '3rem', height: '3rem' }}>
          <span className="visually-hidden">Loading...</span>
        </div>
        <h5 className="mb-2">Loading Course Details...</h5>
        <p className="mb-0">Please wait while we fetch the course information.</p>
      </div>
    </div>
  );

  return (
    <div className="container">
      {/* Loading Overlays */}
      {isSubmitting && <SubmissionLoadingOverlay />}
      {isLoadingCourse && <CourseLoadingOverlay />}

      {/* Steps Header */}
      <div className="stepper-card">
        <div className="stepper-wrapper">
          {/* Background Line */}
          <div className="stepper-line-bg"></div>
          {/* Progress Line */}
          <div 
            className="stepper-line-progress"
            style={{ 
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`
            }}
          ></div>
          
          {/* Step Indicators */}
          <div className="stepper-content">
            {steps.map((step, index) => (
              <div 
                key={step.number} 
                className="stepper-item"
              >
                <div 
                  className={`stepper-circle ${
                    currentStep > index + 1 ? 'completed' :
                    currentStep === index + 1 ? 'active' : ''
                  }`}
                >
                  {step.number}
                </div>
                <div className={`stepper-label ${currentStep === index + 1 ? 'active' : ''}`}>
                  {step.title}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="stepper-card">
        {renderStepContent()}
      </div>

      {/* Navigation Buttons */}
      <div className="stepper-card">
        <div className="stepper-content">
          <button
            className="btn-previous"
            onClick={handlePrevious}
            disabled={currentStep === 1 || isSubmitting}
          >
            <Icon icon="fluent:arrow-left-24-regular" width="20" height="20" />
            Previous
          </button>
          {currentStep === steps.length ? (
            <button
              className="btn-next"
              onClick={handleSubmit}
              disabled={!isCurrentStepValid() || isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="spinner-border spinner-border-sm me-2" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  {courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...'}
                </>
              ) : (
                <>
                  <Icon icon="fluent:checkmark-24-regular" width="20" height="20" className="me-2" />
                  {courseId && decodedCourseId ? 'Update Course' : 'Submit Course'}
                </>
              )}
            </button>
          ) : (
            <button
              className="btn-next"
              onClick={handleNext}
              disabled={!isCurrentStepValid()}
            >
              {currentStep === steps.length - 1 ? 'Preview' : 'Next'}
              <Icon icon="fluent:arrow-right-24-regular" width="20" height="20" />
            </button>
          )}
        </div>
      </div>

    </div>
  );
}

export default CreateCourse;
